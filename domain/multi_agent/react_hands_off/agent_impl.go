package react_hands_off

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/cloudwego/eino/schema"
	"github.com/pkg/errors"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/stone/codedancer_agent/consts"
	"code.byted.org/stone/codedancer_agent/domain/chat"
	"code.byted.org/stone/codedancer_agent/domain/file_process"
	"code.byted.org/stone/codedancer_agent/domain/module_agent/coding_react"
	"code.byted.org/stone/codedancer_agent/domain/module_agent/plan_react"
	"code.byted.org/stone/codedancer_agent/domain/module_agent/project_builder"
	"code.byted.org/stone/codedancer_agent/domain/module_agent/requirement_common"
	"code.byted.org/stone/codedancer_agent/domain/module_agent/summary"
	"code.byted.org/stone/codedancer_agent/domain/multi_agent/react_hands_on"
	"code.byted.org/stone/codedancer_agent/domain/pack_service"
	"code.byted.org/stone/codedancer_agent/domain/project"
	"code.byted.org/stone/codedancer_agent/kitex_gen/codedancer/core_service/coding_agent/common"
	"code.byted.org/stone/codedancer_agent/model"
	"code.byted.org/stone/codedancer_agent/pkg/dispatcher"
	"code.byted.org/stone/codedancer_agent/pkg/mario"
	"code.byted.org/stone/codedancer_agent/pkg/mysql"
	infra_pack_service "code.byted.org/stone/codedancer_agent/pkg/pack_service"
	"code.byted.org/stone/codedancer_agent/pkg/tcc"
	"code.byted.org/stone/codedancer_agent/utils"
	"code.byted.org/stone/codedancer_agent/utils/errorx"
)

// TODO 需要梳理一下，确保状态流转失败的时候，消息也能被再次消费

var (
	responseUserRegex = regexp.MustCompile(consts.ResponseToUserRegex)
)

func NewAgent(chatAgent chat.ChatAgent) Agent {
	return &agentImpl{
		ChatAgent: chatAgent,

		projectService:     project.GetProjectService(),
		chatHistoryService: chat.GetChatHistoryService(),
	}
}

type agentImpl struct {
	ChatAgent chat.ChatAgent

	projectService     project.ProjectService
	chatHistoryService chat.ChatHistoryService
}

func (c *agentImpl) ExecuteHandsOff(ctx context.Context, params *ExecuteHandsOffParams) (err error) {
	if err = utils.Validate(params); err != nil {
		return errors.Wrapf(err, "invalid param")
	}

	userChatMessage, err := chat.GetChatHistoryService().GetChatMessageByID(mysql.DB.UseWriteDB(ctx), params.UserChatMessageID, false)
	if err != nil {
		return errorx.Wrapf(err, "method=ChatHistoryService.GetChatMessageByID")
	}
	switch params.HandlePhase {
	case common.ProjectPhase_Creation, common.ProjectPhase_Requirement:
		err = c.handleRequirement(ctx, params)
	case common.ProjectPhase_Plan:
		frontierClient := chat.GetFrontierClient(&model.FrontierPushConfig{PushTargets: params.PushTargets})
		pushSignalParams := &chat.PushSignalCommonParams{
			ProjectID:          params.Project.ID,
			AssistantMessageID: params.AssistantChatMessageID,
			UserMessageID:      params.UserChatMessageID,
		}
		frontierClient.PushChatStart(ctx, pushSignalParams)
		frontierClient.PushCheckCodingStatus(ctx, pushSignalParams, userChatMessage.VersionID)
		frontierClient.PushChatFinish(ctx, pushSignalParams)
		err = c.handlePlan(ctx, params)
	case common.ProjectPhase_Coding:
		if params.Options.IsRetry {
			params.CodingMessage, err = c.getRetryCodingMessage(ctx, params)
			if err != nil {
				return errorx.Wrapf(err, "method=(*agentImpl).getRetryCodingMessage failed")
			}
			params.Options.IsRetry = false
			plan, err := file_process.GetPlanService().GetReactV1Plan(ctx, params.ProjectID)
			if err != nil {
				return errorx.Wrapf(err, "method=PlanService.GetReactV1Plan failed")
			}
			if plan == nil || len(plan.PageList) <= 0 {
				return errorx.InvalidProjectPlanError
			}
			if plan.CheckPlanCompletion(!params.Options.SkipCodeReview) {
				params.CodingMessage.Stage = consts.HandsOffCodingStageBuilding
			} else {
				params.CodingMessage.Stage = consts.HandsOffCodingStageCoding
			}
		}
		switch params.CodingMessage.Stage {
		case consts.HandsOffCodingStageCoding:
			err = c.handleCoding(ctx, params)
		case consts.HandsOffCodingStageBuilding:
			err = c.handleBuilding(ctx, params)
		case consts.HandsOffCodingStageSummary:
			err = c.handleSummary(ctx, params)
		default:
			err = c.handleCoding(ctx, params)
		}
	case common.ProjectPhase_Enhancing:
		err = c.handleEnhancing(ctx, params)
	default:
		logs.CtxInfo(ctx, "[ReactHandsOff.ExecuteHandsOff] phase is not supported, phase: %v", params.HandlePhase)
	}
	if err != nil {
		logs.CtxError(ctx, "[ReactHandsOff.ExecuteHandsOff] handle failed, err: %+v", err)
		return err
	}

	return nil
}

func (c *agentImpl) handleRequirement(ctx context.Context, message *ExecuteHandsOffParams) (err error) {
	if message.Project.Phase != common.ProjectPhase_Creation && message.Project.Phase != common.ProjectPhase_Requirement {
		logs.CtxWarn(ctx, "[ReactHandsOff.handleRequirement] project phase is not valid, phase: %v", message.Project.Phase)
		return nil
	}

	err = c.ChatAgent.WithSession(ctx, message.UserChatMessageID, func(sessionCtx context.Context) (sessionFinish bool, err error) {
		err = c.projectService.UpdatePhase(sessionCtx, &project.UpdateProjectPhaseParams{
			ProjectID: message.ProjectID,
			Phase:     common.ProjectPhase_Requirement,
		})
		if err != nil {
			return false, errorx.Wrapf(err, "method=ProjectService.UpdatePhase failed, target phase: %v", message.Project.Phase)
		}
		_, hasComplete, err := requirement_common.NewAgent(c.ChatAgent).GenerateRequirement(sessionCtx, &requirement_common.RequirementGenerateParams{
			ExecutionID:     message.ExecutionID,
			ChatSession:     message.ChatSession,
			AssistantChatID: message.AssistantChatMessageID,
			Project:         message.Project,
			MaxChatCounts:   tcc.GetAgentConfig(sessionCtx).MaxChatTimes,
		})
		if err != nil {
			updateErr := message.ChatSession.UpdateSessionToFail(ctx, err)
			if updateErr != nil {
				logs.CtxError(sessionCtx, "method=method=ChatSession.UpdateSessionToFail failed, err=%+v", updateErr)
				return false, errorx.Wrapf(err, "method=Agent.GenerateRequirement failed")
			}
			// todo 加了这个可能有bug
			// _ = file_process.GetRequirementService().DeleteCodingAgentRequirement(sessionCtx, message.ProjectID)
			logs.CtxError(sessionCtx, "method=[Agent.handleRequirement] failed, err=%+v", err)
			logs.CtxWarn(sessionCtx, "error has been ignored")
			return true, nil
		}
		targetChatMessageStatus := common.ChatMessageStatus_Completed
		targetProjectPhase := common.ProjectPhase_Requirement
		if hasComplete {
			targetChatMessageStatus = common.ChatMessageStatus_Processing
			targetProjectPhase = common.ProjectPhase_Plan
			defer func() {
				planTask, err := message.ChatSession.CreateNextChatTasks(ctx, &chat.CreateNextChatTasksParams{
					Name: common.ChatTaskName_PlanGeneration,
					Config: &model.ChatTaskConfig{
						ChatStage:     common.ChatStage_Planing,
						PushTargets:   message.PushTargets,
						RuntimeOption: &message.Options,
					},
					Type: common.ChatTaskType_Stage,
				})
				if err != nil {
					logs.CtxError(sessionCtx, "[Agent.handleRequirement] method=ChatSession.CreateNextChatTasks failed, err=%+v", err)
				}
				message.ChatSession.UpdateTaskStatus(ctx, common.ChatTaskStatus_Completed)
				message.AgentExecutionBasicInfo.ChatTaskID = planTask.ID
				err = dispatcher.GetClient().SendMessage(ctx, &dispatcher.Message{
					DispatcherTag: consts.DispatcherTagReactHandsOff,
					Body: &model.RMQAgentManagerMessage{
						AgentExecutionBasicInfo: message.AgentExecutionBasicInfo,
						HandsOffMessage: &model.RMQReactHandsOffMessage{
							HandlePhase: common.ProjectPhase_Plan,
						},
					},
				})
				if err != nil {
					err = errorx.Wrapf(err, "method=Dispatcher.SendMessage failed")
				}
			}()
		} else {
			sessionFinish = true
			message.ChatSession.UpdateTaskStatus(ctx, common.ChatTaskStatus_Completed)
			infra_pack_service.GetClient().UpdateCozeTask(ctx, &infra_pack_service.UpdateCozeTaskReq{
				ProjectID:  message.ProjectID,
				UserID:     message.Project.UserID,
				TaskStatus: 6, // 中断（agent发起的中断，等待用户确认）
			})
		}
		err = chat.GetChatCombinationService().UpdateChatMessageStatus(sessionCtx, &chat.UpdateChatMessageStatusParams{
			ProjectID:               message.Project.ID,
			ExecutionID:             message.ExecutionID,
			UserChatMessageID:       message.UserChatMessageID,
			AssistantChatMessageID:  message.AssistantChatMessageID,
			TargetChatMessageStatus: targetChatMessageStatus,
			TargetProjectPhase:      targetProjectPhase,
		})
		if err != nil {
			return false, errorx.Wrapf(err, "method=ChatCombinationService.UpdateChatMessageStatusToComplete failed")
		}
		return sessionFinish, nil
	})
	if err != nil {
		return err
	}

	return nil
}

func (c *agentImpl) handlePlan(ctx context.Context, message *ExecuteHandsOffParams) (err error) {
	if message.Project.Phase != common.ProjectPhase_Plan {
		logs.CtxWarn(ctx, "[ReactHandsOff.handlePlan] project phase is not valid, phase: %v", message.Project.Phase)
		return nil
	}

	var plan *model.ReactV1Plan
	err = c.ChatAgent.WithSession(ctx, message.UserChatMessageID, func(sessionCtx context.Context) (sessionFinish bool, err error) {
		plan, err = plan_react.NewAgent(c.ChatAgent).GeneratePlan(ctx, &plan_react.PlanGenerateParams{
			ExecutionID:       message.ExecutionID,
			ChatSession:       message.ChatSession,
			AssistantChatID:   message.AssistantChatMessageID,
			Project:           message.Project,
			UserChatMessageID: message.UserChatMessageID,
			Options:           &message.Options,
		})
		if err != nil {
			updateErr := message.ChatSession.UpdateSessionToFail(ctx, err)
			if updateErr != nil {
				logs.CtxError(sessionCtx, "method=ChatSession.UpdateSessionToFail failed, err=%+v", updateErr)
				return false, errorx.Wrapf(err, "method=Agent.GeneratePlan failed")
			}
			file_process.GetPlanService().DeleteReactV1Plan(sessionCtx, message.ProjectID)
			logs.CtxError(sessionCtx, "method=[Agent.handlePlan] failed, err=%+v", err)
			logs.CtxWarn(sessionCtx, "error has been ignored")
			return true, nil
		}
		if err = c.createRepository(ctx, message.Project, message.Project.ProjectTemplate); err != nil {
			return false, errorx.Wrapf(err, "method=(*AgentImpl).createRepository failed")
		}
		err = chat.GetChatCombinationService().UpdateChatMessageStatus(ctx, &chat.UpdateChatMessageStatusParams{
			ProjectID:               message.Project.ID,
			ExecutionID:             message.ExecutionID,
			UserChatMessageID:       message.UserChatMessageID,
			AssistantChatMessageID:  message.AssistantChatMessageID,
			TargetChatMessageStatus: common.ChatMessageStatus_Processing,
			TargetProjectPhase:      common.ProjectPhase_Coding,
		})
		if err != nil {
			return false, errorx.Wrapf(err, "method=ChatCombinationService.UpdateChatMessageStatusToComplete failed")
		}
		return false, nil
	})
	if err != nil {
		return err
	}
	if plan == nil {
		return nil
	}
	// todo 后续需要考虑一下这里失败了，重入的时候幂等
	_, err = message.ChatSession.CreateNextChatTasks(ctx, ConvertPlan2CreateNextChatTasksParams(ctx, plan, &model.ChatTaskConfig{
		ChatStage:     common.ChatStage_Coding,
		PushTargets:   message.PushTargets,
		RuntimeOption: &message.Options,
	}))
	if err != nil {
		return errorx.Wrapf(err, "method=ChatSession.CreateNextChatTasks failed")
	}
	// todo 这里的逻辑后续都要改掉，现在任务执行是由chat触发，读chat_history表；后续要改成读task表，然后以dfs的方式执行task树，每个workflow、stage都是一个可执行节点，并且每个workflow、stage、action都要注册对应的handler
	message.ChatSession.UpdateTaskStatus(ctx, common.ChatTaskStatus_Completed)
	nextTask, err := message.ChatSession.GetNextExecutablePendingTask(ctx)
	if err != nil {
		return errorx.Wrapf(err, "method=ChatSession.GetNextExecutablePendingTask failed")
	}
	if nextTask == nil {
		updateErr := message.ChatSession.UpdateSessionToFail(ctx, err)
		if updateErr != nil {
			logs.CtxError(ctx, "method=ChatSession.UpdateSessionToFail failed, err=%+v", updateErr)
		}
		updateErr = project.GetProjectService().UpdatePhase(ctx, &project.UpdateProjectPhaseParams{
			ProjectID: message.ProjectID,
			Phase:     common.ProjectPhase_Requirement,
		})
		if updateErr != nil {
			logs.CtxError(ctx, "method=ProjectService.UpdatePhase failed, err=%+v", updateErr)
		}
		return errorx.Wrapf(err, "method=ChatSession.GetNextExecutablePendingTask empty")
	}
	message.AgentExecutionBasicInfo.ChatTaskID = nextTask.ID
	err = dispatcher.GetClient().SendMessage(ctx, &dispatcher.Message{
		DispatcherTag: consts.DispatcherTagReactHandsOff,
		Body: &model.RMQAgentManagerMessage{
			AgentExecutionBasicInfo: message.AgentExecutionBasicInfo,
			HandsOffMessage: &model.RMQReactHandsOffMessage{
				HandlePhase: common.ProjectPhase_Coding,
				CodingMessage: model.RMQReactHandsOffCodingSubMessage{
					Stage:           consts.HandsOffCodingStageCoding,
					HandlePageIndex: 0,
					IsReview:        false,
				},
			},
		},
	})
	if err != nil {
		return errorx.Wrapf(err, "method=Dispatcher.SendMessage failed")
	}

	return nil
}

// TODO check幂等
func (c *agentImpl) handleCoding(ctx context.Context, message *ExecuteHandsOffParams) (err error) {
	if message.Project.Phase != common.ProjectPhase_Coding {
		logs.CtxWarn(ctx, "[ReactHandsOff.handleCoding] project phase is not valid, phase: %v", message.Project.Phase)
		return nil
	}

	plan, err := file_process.GetPlanService().GetReactV1Plan(ctx, message.ProjectID)
	if err != nil {
		return errorx.Wrapf(err, "method=PlanService.GetReactV1Plan failed")
	}
	if plan == nil || len(plan.PageList) <= 0 {
		return errorx.InvalidProjectPlanError
	}
	if plan.CheckPageCompletion(message.CodingMessage.HandlePageIndex, message.CodingMessage.IsReview && !message.Options.SkipCodeReview) {
		logs.CtxInfo(ctx, "[Agent.handleCoding] page is completed")
		return nil
	}
	currentPage := plan.PageList[message.CodingMessage.HandlePageIndex]
	invoke := func(ctx context.Context, fs model.FileSystem) error {
		pack_service.GetQuotaControlService().AcquireSeat(ctx, &pack_service.AcquireSeatParams{
			ProjectID: message.ProjectID,
			UserID:    message.Project.UserID,
		})
		params := &coding_react.CodeGenerateV1Params{
			ExecutionID:       message.ExecutionID,
			ChatSession:       message.ChatSession,
			AssistantChatID:   message.AssistantChatMessageID,
			Project:           message.Project,
			FileSystem:        fs,
			MaxLintCounts:     tcc.GetAgentConfig(ctx).MaxLintTimes,
			MaxChatCounts:     tcc.GetAgentConfig(ctx).MaxChatTimes,
			PlanPage:          currentPage,
			UserChatMessageID: message.UserChatMessageID,
			LongTermMemory:    c.genLongTermMemory(plan),
			Options:           &message.Options,
		}
		if !message.CodingMessage.IsReview {
			assistantMessages, err := coding_react.NewAgentV1(c.ChatAgent).GenerateCode(ctx, params)
			if err != nil {
				return errorx.Wrapf(err, "method=coding_react.AgentV1.GenerateCode failed")
			}
			currentPage.HasDone = true
			currentPage.GenerationSummary = c.extractResponseToUserMessage(assistantMessages)
		} else {
			assistantMessages, err := coding_react.NewAgentV1(c.ChatAgent).ReviewCode(ctx, &coding_react.CodeReviewParams{
				CodeGenerateV1Params: params,
			})
			if err != nil {
				return errorx.Wrapf(err, "method=coding_react.AgentV1.CodeReviewParams failed")
			}
			currentPage.HasReview = true
			currentPage.ReviewSummary = c.extractResponseToUserMessage(assistantMessages)
		}
		return nil
	}
	needContinue := true
	err = c.ChatAgent.WithSession(ctx, message.UserChatMessageID, func(sessionCtx context.Context) (sessionFinish bool, err error) {
		chatMessage, err := c.chatHistoryService.GetChatMessageByID(sessionCtx, message.UserChatMessageID, false)
		if err != nil {
			return false, errorx.Wrapf(err, "method=ChatHistoryService.GetChatMessageByID failed")
		}
		hasCommitted, err := file_process.GetGitService().WithProjectCommitSession(sessionCtx, &file_process.ProjectCommitSessionParams{
			ProjectDO:         message.Project,
			DraftVersionID:    chatMessage.VersionID,
			IsInvisibleToUser: true,
			ModifyFunc: func(ctx context.Context, fs model.FileSystem) (bool, error) {
				err = invoke(ctx, fs)
				if err != nil {
					return false, err
				}
				return true, nil
			},
		})
		if err != nil {
			updateErr := message.ChatSession.UpdateSessionToFail(ctx, err)
			if updateErr != nil {
				logs.CtxError(sessionCtx, "method=ChatSession.UpdateSessionToFail failed, err=%+v", updateErr)
				return false, errorx.Wrapf(err, "method=Agent.GenerateCode/Agent.ReviewCode failed")
			}
			logs.CtxError(sessionCtx, "method=[Agent.handleCoding] failed, err=%+v", err)
			logs.CtxWarn(sessionCtx, "error has been ignored")
			needContinue = false
			return true, nil
		}
		if hasCommitted {
			newDraftID, err := pack_service.GetVersionControlService().GetDraftVersionID(ctx, &pack_service.GetDraftVersionIDParams{
				ProjectID: message.Project.ID,
				UserID:    message.Project.UserID,
			})
			if err != nil {
				return false, errorx.Wrapf(err, "method=VersionControlService.GetDraftVersionID failed")
			}
			_, err = c.chatHistoryService.UpdateChatMessage(ctx, &chat.UpdateChatMessageParams{
				ProjectID:       message.Project.ID,
				ChatMessageIDs:  []int64{message.UserChatMessageID, message.AssistantChatMessageID},
				UpdateVersionID: conv.Int64Ptr(newDraftID),
			})
			if err != nil {
				return false, errorx.Wrapf(err, "method=ChatHistoryService.UpdateChatMessage failed")
			}
		}
		return false, nil
	})
	if err != nil {
		return err
	}
	if !needContinue {
		return nil
	}
	plan, err = file_process.GetPlanService().SaveReactV1Plan(ctx, message.ProjectID, plan)
	if err != nil {
		return errorx.Wrapf(err, "method=PlanService.SaveReactV1Plan failed")
	}
	if plan.CheckPlanCompletion(!message.Options.SkipCodeReview) {
		err = chat.GetChatLogService().UpdateChatLogStatus(ctx, &chat.UpdateChatLogStatusParams{
			ProjectID:              message.ProjectID,
			AssistantChatMessageID: message.AssistantChatMessageID,
			ExecutionID:            message.ExecutionID,
			Statuses: []common.ChatLogStatus{
				common.ChatLogStatus_Processing,
			},
			UpdateToStatus: common.ChatLogStatus_Completed,
		})
		if err != nil {
			return errorx.Wrapf(err, "method=ChatLogService.UpdateChatLogStatus failed")
		}
		stage := consts.HandsOffCodingStageSummary
		if message.Options.EnableAutoBuild {
			stage = consts.HandsOffCodingStageBuilding
		}
		nextTask, err := message.ChatSession.GetNextExecutablePendingTask(ctx)
		if err != nil {
			return errorx.Wrapf(err, "method=ChatSession.GetNextExecutablePendingTask failed")
		}
		if nextTask == nil {
			updateErr := message.ChatSession.UpdateSessionToFail(ctx, err)
			if updateErr != nil {
				logs.CtxError(ctx, "method=ChatSession.UpdateSessionToFail failed, err=%+v", updateErr)
			}
			updateErr = project.GetProjectService().UpdatePhase(ctx, &project.UpdateProjectPhaseParams{
				ProjectID: message.ProjectID,
				Phase:     common.ProjectPhase_Plan,
			})
			if updateErr != nil {
				logs.CtxError(ctx, "method=ProjectService.UpdatePhase failed, err=%+v", updateErr)
			}
			return errorx.Wrapf(err, "method=ChatSession.GetNextExecutablePendingTask empty")
		}
		message.ChatSession.UpdateTaskStatus(ctx, common.ChatTaskStatus_Completed)
		message.AgentExecutionBasicInfo.ChatTaskID = nextTask.ID
		// todo 如果消息发送失败，重试再进来的时候，就会被前面的判断page是否完成的逻辑给直接return了，导致整个0到1阶段卡住，这个需要优化
		err = dispatcher.GetClient().SendMessage(ctx, &dispatcher.Message{
			DispatcherTag: consts.DispatcherTagReactHandsOff,
			Body: &model.RMQAgentManagerMessage{
				AgentExecutionBasicInfo: message.AgentExecutionBasicInfo,
				HandsOffMessage: &model.RMQReactHandsOffMessage{
					HandlePhase: common.ProjectPhase_Coding,
					CodingMessage: model.RMQReactHandsOffCodingSubMessage{
						Stage: stage,
					},
				},
			},
		})
		if err != nil {
			return errorx.Wrapf(err, "method=Dispatcher.SendMessage failed")
		}
	} else {
		err = chat.GetChatLogService().UpdateChatLogStatus(ctx, &chat.UpdateChatLogStatusParams{
			ProjectID:              message.ProjectID,
			AssistantChatMessageID: message.AssistantChatMessageID,
			ExecutionID:            message.ExecutionID,
			Statuses: []common.ChatLogStatus{
				common.ChatLogStatus_Processing,
			},
			UpdateToStatus: common.ChatLogStatus_Completed,
		})
		if err != nil {
			return errorx.Wrapf(err, "method=ChatLogService.UpdateChatLogStatus failed")
		}
		nextTask, err := message.ChatSession.GetNextExecutablePendingTask(ctx)
		if err != nil {
			return errorx.Wrapf(err, "method=ChatSession.GetNextExecutablePendingTask failed")
		}
		if nextTask == nil {
			logs.CtxError(ctx, "[react_hands_off.Agent] method=ChatSession.GetNextExecutablePendingTask failed, nextTask is nil, message=%+v", utils.Jsonify(message))
			updateErr := message.ChatSession.UpdateSessionToFail(ctx, err)
			if updateErr != nil {
				logs.CtxError(ctx, "method=ChatSession.UpdateSessionToFail failed, err=%+v", updateErr)
			}
			updateErr = project.GetProjectService().UpdatePhase(ctx, &project.UpdateProjectPhaseParams{
				ProjectID: message.ProjectID,
				Phase:     common.ProjectPhase_Plan,
			})
			if updateErr != nil {
				logs.CtxError(ctx, "method=ProjectService.UpdatePhase failed, err=%+v", updateErr)
			}
			return errorx.Wrapf(err, "method=ChatSession.GetNextExecutablePendingTask empty")
		}
		message.ChatSession.UpdateTaskStatus(ctx, common.ChatTaskStatus_Completed)
		message.AgentExecutionBasicInfo.ChatTaskID = nextTask.ID
		pageIndex, isReview := plan.GetNextProcessPage()
		err = dispatcher.GetClient().SendMessage(ctx, &dispatcher.Message{
			DispatcherTag: consts.DispatcherTagReactHandsOff,
			Body: &model.RMQAgentManagerMessage{
				AgentExecutionBasicInfo: message.AgentExecutionBasicInfo,
				HandsOffMessage: &model.RMQReactHandsOffMessage{
					HandlePhase: common.ProjectPhase_Coding,
					CodingMessage: model.RMQReactHandsOffCodingSubMessage{
						Stage:           consts.HandsOffCodingStageCoding,
						HandlePageIndex: pageIndex,
						IsReview:        isReview,
					},
				},
			},
		})
		if err != nil {
			return errorx.Wrapf(err, "method=Dispatcher.SendMessage failed")
		}
	}

	return nil
}

func (c *agentImpl) handleBuilding(ctx context.Context, message *ExecuteHandsOffParams) (err error) {
	// todo 丰富phase
	if message.Project.Phase != common.ProjectPhase_Coding {
		logs.CtxWarn(ctx, "[ReactHandsOff.handleBuilding] project phase is not valid, phase: %v", message.Project.Phase)
		return nil
	}

	fs, err := file_process.GetGitService().GetRepositoryFromProject(ctx, message.Project)
	if err != nil {
		return errorx.Wrapf(err, "method=GitService.GetRepository failed")
	}
	isSuccess, err := project_builder.NewAgentV1(c.ChatAgent).BuildProject(ctx, &project_builder.BuildProjectParams{
		ExecutionID:            message.ExecutionID,
		ChatSession:            message.ChatSession,
		GitRepository:          fs,
		Project:                message.Project,
		UserChatMessageID:      message.UserChatMessageID,
		AssistantChatMessageID: message.AssistantChatMessageID,
		Options:                message.Options,
	})
	if err != nil && !errors.Is(err, errorx.AgentBuildProjectTimeoutError) {
		// updateErr := message.ChatSession.UpdateSessionToFail(ctx, err)
		// if updateErr != nil {
		// 	logs.CtxError(ctx, "method=ChatSession.UpdateSessionToFail failed, err=%+v", updateErr)
		// 	return errorx.Wrapf(err, "method=BuildAgent.BuildProject failed")
		// }
		logs.CtxError(ctx, "method=[Agent.handleBuilding] failed, err=%+v", err)
		logs.CtxWarn(ctx, "error has been ignored")
	}
	if errors.Is(err, errorx.AgentBuildProjectTimeoutError) {
		logs.CtxWarn(ctx, "method=BuildAgent.BuildProject timeout, ignore errors")
	}
	err = chat.GetChatLogService().UpdateChatLogStatus(ctx, &chat.UpdateChatLogStatusParams{
		ProjectID:              message.ProjectID,
		AssistantChatMessageID: message.AssistantChatMessageID,
		ExecutionID:            message.ExecutionID,
		Statuses: []common.ChatLogStatus{
			common.ChatLogStatus_Processing,
		},
		UpdateToStatus: common.ChatLogStatus_Completed,
	})
	if err != nil {
		return errorx.Wrapf(err, "method=ChatLogService.UpdateChatLogStatus failed")
	}
	stage := consts.HandsOffCodingStageSummary
	buildTimes := message.CodingMessage.BuildTimes + 1
	if !isSuccess && buildTimes < tcc.GetAgentConfig(ctx).MaxBuildTimes {
		stage = consts.HandsOffCodingStageBuilding
		_, err = message.ChatSession.CreateNextChatTasks(ctx, &chat.CreateNextChatTasksParams{
			Name: common.ChatTaskName_CodeTesting,
			Config: &model.ChatTaskConfig{
				ChatStage:   common.ChatStage_Building,
				PushTargets: message.PushTargets,
				Parameter: &model.TaskParameter{
					TaskTitle: "测试",
					PageParameter: &model.TaskPageParameter{
						PageName: "测试",
					},
				},
				RuntimeOption: &message.Options,
			},
			Type: common.ChatTaskType_Stage,
		})
	}
	nextTask, err := message.ChatSession.GetNextExecutablePendingTask(ctx)
	if err != nil {
		return errorx.Wrapf(err, "method=ChatSession.GetNextExecutablePendingTask failed")
	}
	if nextTask == nil {
		updateErr := message.ChatSession.UpdateSessionToFail(ctx, err)
		if updateErr != nil {
			logs.CtxError(ctx, "method=ChatSession.UpdateSessionToFail failed, err=%+v", updateErr)
		}
		updateErr = project.GetProjectService().UpdatePhase(ctx, &project.UpdateProjectPhaseParams{
			ProjectID: message.ProjectID,
			Phase:     common.ProjectPhase_Plan,
		})
		if updateErr != nil {
			logs.CtxError(ctx, "method=ProjectService.UpdatePhase failed, err=%+v", updateErr)
		}
		return errorx.Wrapf(err, "method=ChatSession.GetNextExecutablePendingTask empty")
	}
	message.ChatSession.UpdateTaskStatus(ctx, common.ChatTaskStatus_Completed)
	message.AgentExecutionBasicInfo.ChatTaskID = nextTask.ID
	err = dispatcher.GetClient().SendMessage(ctx, &dispatcher.Message{
		DispatcherTag: consts.DispatcherTagReactHandsOff,
		Body: &model.RMQAgentManagerMessage{
			AgentExecutionBasicInfo: message.AgentExecutionBasicInfo,
			HandsOffMessage: &model.RMQReactHandsOffMessage{
				HandlePhase: common.ProjectPhase_Coding,
				CodingMessage: model.RMQReactHandsOffCodingSubMessage{
					Stage:      stage,
					BuildTimes: buildTimes,
				},
			},
		},
	})
	if err != nil {
		return errorx.Wrapf(err, "method=Dispatcher.SendMessage failed")
	}
	if stage == consts.HandsOffCodingStageSummary {
		plan, err := file_process.GetPlanService().GetReactV1Plan(ctx, message.ProjectID)
		if err != nil {
			return errorx.Wrapf(err, "method=PlanService.GetReactV1Plan failed")
		}
		if plan.AutoBuild == nil {
			plan.AutoBuild = model.NewAutoBuildPlanPage()
		}
		plan.AutoBuild.HasDone = true
		plan, err = file_process.GetPlanService().SaveReactV1Plan(ctx, message.ProjectID, plan)
		if err != nil {
			return errorx.Wrapf(err, "method=PlanService.SaveReactV1Plan failed")
		}
	}

	return nil
}

func (c *agentImpl) handleSummary(ctx context.Context, message *ExecuteHandsOffParams) (err error) {
	if message.Project.Phase != common.ProjectPhase_Coding {
		logs.CtxWarn(ctx, "[ReactHandsOff.handleEnhancing] project phase is not valid, phase: %v", message.Project.Phase)
		return nil
	}
	// todo 加一个状态去check任务是不是到了summary节点？

	err = c.ChatAgent.WithSession(ctx, message.UserChatMessageID, func(sessionCtx context.Context) (sessionFinish bool, err error) {
		chatMessage, err := c.chatHistoryService.GetChatMessageByID(sessionCtx, message.UserChatMessageID, false)
		if err != nil {
			return false, errorx.Wrapf(err, "method=ChatHistoryService.GetChatMessageByID failed")
		}
		plan, err := file_process.GetPlanService().GetReactV1Plan(sessionCtx, message.ProjectID)
		if err != nil {
			return false, errorx.Wrapf(err, "method=PlanService.GetReactV1Plan failed")
		}
		_, err = file_process.GetGitService().WithProjectCommitSession(sessionCtx, &file_process.ProjectCommitSessionParams{
			ProjectDO:         message.Project,
			DraftVersionID:    chatMessage.VersionID,
			IsInvisibleToUser: false,
			ModifyFunc: func(ctx context.Context, fs model.FileSystem) (bool, error) {
				err = summary.NewAgentV1(c.ChatAgent).GenerateSummary(ctx, &summary.SummaryGenerateParams{
					ExecutionID:     message.ExecutionID,
					ChatSession:     message.ChatSession,
					AssistantChatID: message.AssistantChatMessageID,
					Project:         message.Project,
					FileSystem:      fs,
					Plan:            plan,
				})
				if err != nil {
					return false, errorx.Wrapf(err, "method=SummaryAgentV1.GenerateSummary failed")
				}
				return true, nil
			},
			PostFunc: func(ctx context.Context) error {
				return chat.GetChatHistoryService().CreateChatMessageByType(ctx, &chat.CreateChatMessageByTypeParams{
					UserID:    message.Project.UserID,
					ProjectID: message.Project.ID,
					Type:      common.ChatMessageType_Version,
					VersionMessage: &model.VersionMessage{
						Message: &model.Message{
							Role: common.MessageRole_OperationSystem,
						},
					},
				})
			},
		})
		if err != nil {
			updateErr := message.ChatSession.UpdateSessionToFail(ctx, err)
			if updateErr != nil {
				logs.CtxError(sessionCtx, "method=ChatSession.UpdateSessionToFail failed, err=%+v", updateErr)
				return false, errorx.Wrapf(err, "method=Agent.GenerateSummary failed")
			}
			logs.CtxError(sessionCtx, "method=[Agent.handleSummary] failed, err=%+v", err)
			logs.CtxWarn(sessionCtx, "error has been ignored")
			return true, nil
		}
		message.ChatSession.UpdateTaskStatus(ctx, common.ChatTaskStatus_Completed)
		err = chat.GetChatCombinationService().UpdateChatMessageStatusToComplete(sessionCtx, &chat.UpdateChatMessageToCompleteParams{
			ProjectID:              message.Project.ID,
			ExecutionID:            message.ExecutionID,
			UserChatMessageID:      message.UserChatMessageID,
			AssistantChatMessageID: message.AssistantChatMessageID,
			TargetProjectPhase:     common.ProjectPhase_Enhancing,
		})
		if err != nil {
			return false, errorx.Wrapf(err, "method=ChatCombinationService.UpdateChatMessageStatusToComplete failed")
		}
		mario.GetMarioClient().ReportHandsOffDuration(ctx, &mario.ReportHandsOffDurationParams{
			Stage:         mario.CodingStageCoding,
			ProjectID:     message.ProjectID,
			UserMessageID: message.UserChatMessageID,
			CostDuration:  time.Now().Sub(chatMessage.CreatedAt),
		})
		infra_pack_service.GetClient().UpdateCozeTask(ctx, &infra_pack_service.UpdateCozeTaskReq{
			ProjectID: message.ProjectID,
			UserID:    message.Project.UserID,
			// TaskFinish = 3, // 一轮任务完成
			TaskStatus: 3,
		})
		return true, nil
	})
	if err != nil {
		return err
	}

	return nil
}

func (c *agentImpl) handleEnhancing(ctx context.Context, message *ExecuteHandsOffParams) (err error) {
	if message.Project.Phase != common.ProjectPhase_Enhancing {
		logs.CtxWarn(ctx, "[ReactHandsOff.handleEnhancing] project phase is not valid, phase: %v", message.Project.Phase)
		return nil
	}

	err = react_hands_on.NewAgent(c.ChatAgent).ExecuteHandsOn(ctx, &react_hands_on.ExecuteHandsOnParams{
		AgentExecutionBasicInfo: message.AgentExecutionBasicInfo,
		Project:                 message.Project,
		ChatSession:             message.ChatSession,
	})
	if err != nil {
		return errorx.Wrapf(err, "method=react_hands_on.Agent.ExecuteHandsOn failed")
	}

	return nil
}

func (c *agentImpl) genLongTermMemory(plan *model.ReactV1Plan) []*schema.Message {
	if len(plan.ImgCommands) <= 0 {
		return nil
	}

	longTermMemory := make([]*schema.Message, 0)
	multiContent := make([]schema.ChatMessagePart, 0)
	multiContent = append(multiContent, schema.ChatMessagePart{
		Type: schema.ChatMessagePartTypeText,
		Text: "<user_image_commands>",
	})
	for _, imgCommand := range plan.ImgCommands {
		multiContent = append(multiContent, schema.ChatMessagePart{
			Type: schema.ChatMessagePartTypeText,
			Text: fmt.Sprintf("<image_purpose>%s</image_purpose>\n<images_data_path>%s</images_data_path>\n origin image: ", imgCommand.UserCommand, imgCommand.ImgURL),
		})
		multiContent = append(multiContent, schema.ChatMessagePart{
			Type: schema.ChatMessagePartTypeImageURL,
			ImageURL: &schema.ChatMessageImageURL{
				URL: imgCommand.ImgURL,
			},
		})
	}
	multiContent = append(multiContent, schema.ChatMessagePart{
		Type: schema.ChatMessagePartTypeText,
		Text: "</user_image_commands>",
	})
	longTermMemory = append(longTermMemory, &schema.Message{
		Role:         schema.User,
		MultiContent: multiContent,
	})
	return longTermMemory
}

func (c *agentImpl) createRepository(ctx context.Context, projectDO *model.Project, giteaProjectTemplate string) error {
	createRepoResp, err := infra_pack_service.GetClient().CreateRepo(ctx, &infra_pack_service.CreateRepoReq{
		ProjectID: projectDO.ID,
		UserID:    projectDO.UserID,
	})
	if err != nil {
		return errorx.Wrapf(err, "method=PackClient.CreateRepo failed")
	}
	fs, err := file_process.GetGitService().GetRepositoryFromRepoInfo(ctx, &file_process.GetRepositoryFromRepoInfoParams{
		RepoName: createRepoResp.RepoName,
		RepoUrl:  createRepoResp.RepoURL,
		Token:    createRepoResp.GitToken.Token,
	})
	if err != nil {
		return errorx.Wrapf(err, "method=GitService.GetRepositoryFromRepoInfo failed")
	}
	if len(giteaProjectTemplate) <= 0 {
		err = coding_react.NewAgentV1(c.ChatAgent).ForkTemplateFiles(ctx, &coding_react.ForkTemplateFilesParams{
			Project:    projectDO,
			FileSystem: fs,
		})
		if err != nil {
			return errorx.Wrapf(err, "method=(*ReactAgentV1).ForkTemplateFiles failed")
		}
	} else {
		err = coding_react.NewAgentV1(c.ChatAgent).ForkTemplateFilesFromGitea(ctx, &coding_react.ForkTemplateFilesParams{
			Project:      projectDO,
			FileSystem:   fs,
			TemplateName: giteaProjectTemplate,
		})
		if err != nil {
			return errorx.Wrapf(err, "method=(*ReactAgentV1).ForkTemplateFilesFromGitea failed")
		}
	}
	err = file_process.GetGitService().SaveRepository(ctx, fs, &model.GitCommitInfo{
		UserName:      "codedancer", // TODO 改成真实的userName
		CommitMessage: model.GitCommitMessage{CommitMessage: "init project template"},
	})
	if err != nil {
		return errorx.Wrapf(err, "method=file_process.GetGitService failed")
	}

	return nil
}

func (c *agentImpl) extractResponseToUserMessage(assistantMessages []string) string {
	messages := strings.Join(assistantMessages, "\n")
	matches := responseUserRegex.FindAllStringSubmatch(messages, -1)
	if len(matches) <= 0 {
		return ""
	}

	return strings.TrimSpace(matches[0][1])
}

func (c *agentImpl) getRetryCodingMessage(ctx context.Context, message *ExecuteHandsOffParams) (resp model.RMQReactHandsOffCodingSubMessage, err error) {
	plan, err := file_process.GetPlanService().GetReactV1Plan(ctx, message.ProjectID)
	if err != nil {
		return resp, errorx.Wrapf(err, "method=PlanService.GetReactV1Plan failed")
	}
	if plan == nil || len(plan.PageList) <= 0 {
		return resp, errorx.InvalidProjectPlanError
	}
	idx, isReview := plan.GetNextProcessPage()
	if idx == -1 {
		return resp, errorx.RetryNotRequiredError
	}

	return model.RMQReactHandsOffCodingSubMessage{
		HandlePageIndex: idx,
		IsReview:        isReview,
	}, nil
}
