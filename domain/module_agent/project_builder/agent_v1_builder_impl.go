package project_builder

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/gopkg/env"

	"github.com/pkg/errors"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/stone/codedancer_agent/domain/chat"
	"code.byted.org/stone/codedancer_agent/domain/file_process"
	"code.byted.org/stone/codedancer_agent/domain/module_agent/coding_react"
	"code.byted.org/stone/codedancer_agent/kitex_gen/codedancer/core_service/coding_agent/common"
	"code.byted.org/stone/codedancer_agent/model"
	"code.byted.org/stone/codedancer_agent/pkg/metrics"
	"code.byted.org/stone/codedancer_agent/pkg/oapi/lint"
	"code.byted.org/stone/codedancer_agent/pkg/tcc"
	"code.byted.org/stone/codedancer_agent/utils"
	"code.byted.org/stone/codedancer_agent/utils/errorx"
)

const FinishPrompt = "<finished/>"

func NewAgentV1(chatAgent chat.ChatAgent) Agent {
	return &AgentImpl{
		chatAgent: chatAgent,
	}
}

type AgentImpl struct {
	chatAgent chat.ChatAgent
}

func (a *AgentImpl) BuildProject(ctx context.Context, params *BuildProjectParams) (isSuccess bool, err error) {
	if err := utils.Validate(params); err != nil {
		return false, err
	}

	for i, maxLintTimes := 0, tcc.GetAgentConfig(ctx).MaxLintTimes; i < maxLintTimes && !params.Options.SkipLint; i++ {
		lintError, err := lint.GetLintClient().CheckProject(ctx, params.GitRepository, params.Project.ID, params.Project.ProjectTemplate)
		if err != nil {
			logs.CtxError(ctx, "method=LintClient.CheckProject failed, err=%+v", err)
			break
		}
		if lintError == nil || len(lintError.GetDiagnoseMessages()) <= 0 {
			logs.CtxInfo(ctx, "method=LintClient.CheckProject lint check passed")
			break
		} else if i+1 >= maxLintTimes {
			logs.CtxWarn(ctx, "method=LintClient.CheckProject lint check exceed max rounds, skip lint check")
			break
		} else {
			logs.CtxWarn(ctx, "method=LintClient.CheckProject lint check failed, lint error=%s", utils.Jsonify(lintError))
			nextUserPromptContent := fmt.Sprintf(`<system_message>
    The code you just modified has some lint errors. 
    Fix them SILENTLY without letting the user know - do not output any messages in <response_to_user> tags and skip the final summary.
    This is the lint error:
    %s
</system_message>`, lintError.GetDiagnoseMessages())
			err = coding_react.NewAgentV0(a.chatAgent).GenerateCode(ctx, &coding_react.GenerateCodeV0Params{
				ExecutionID:            params.ExecutionID,
				ChatSession:            params.ChatSession,
				AssistantChatMessageID: params.AssistantChatMessageID,
				Project:                params.Project,
				GitRepository:          params.GitRepository,
				UserTask:               model.CreateUserMessage(nextUserPromptContent),
				MaxChatTimes:           tcc.GetAgentConfig(ctx).MaxChatTimes,
				MaxToolCallTimes:       tcc.GetAgentConfig(ctx).MaxToolCallTimes,
				MaxLintTimes:           0,
				Options:                &params.Options,
				ChatStage:              common.ChatStage_Building,
				ForbidResponseToUser:   true,
			})
			if err != nil {
				return false, errorx.Wrapf(err, "method=coding_react.AgentV0.GenerateCode failed")
			}
			err = file_process.GetGitService().SaveRepository(ctx, params.GitRepository, &model.GitCommitInfo{
				UserName: "codedancer", // TODO 改成真实的userName
				CommitMessage: model.GitCommitMessage{
					CommitMessage: "lint check",
				},
			})
			if err != nil {
				return false, errorx.Wrapf(err, "method=GitService.SaveRepository failed")
			}
		}
	}

	userChatMessage, err := chat.GetChatHistoryService().GetChatMessageByID(ctx, params.UserChatMessageID, false)
	if err != nil {
		return false, errorx.Wrapf(err, "method=ChatHistoryService.GetChatMessageByID failed")
	}
	messages, err := chat.GetChatHistoryService().BatchCreateChatMessage(ctx, &chat.BatchCreateChatMessageParams{
		CreateChatMessages: []*chat.CreateChatMessageParams{
			{
				ProjectID:    params.Project.ID,
				ParentID:     params.AssistantChatMessageID,
				VersionID:    userChatMessage.VersionID,
				ProjectPhase: userChatMessage.ProjectPhase,
				Role:         common.MessageRole_OperationSystem,
				Type:         common.ChatMessageType_Autofix,
				Status:       common.ChatMessageStatus_Processing,
				LogID:        utils.GetLogID(ctx),
			},
		},
	})
	if err != nil {
		return false, errorx.Wrapf(err, "method=ChatHistoryService.BatchCreateChatMessage failed")
	}
	if len(messages) <= 0 || messages[0] == nil {
		return false, errorx.Wrapf(errorx.InvalidParamError, "method=ChatHistoryService.BatchCreateChatMessage create message failed")
	}
	var buildErrMessage *model.Message
	if env.IsBoeCN() || env.Region() == env.R_CN {
		isSuccess, buildErrMessage, err = a.buildProjectForCN(ctx, params, messages[0].ID)
	} else {
		isSuccess, buildErrMessage, err = a.buildProjectForI18N(ctx, params, messages[0].ID)
	}
	if err != nil {
		return false, errorx.Wrapf(err, "method=Agent.buildProjectForCN/buildProjectForI18N failed")
	}
	if isSuccess {
		logs.CtxInfo(ctx, "[Agent.BuildProject] build project success")
		return true, nil
	}
	logs.CtxInfo(ctx, "[Agent.BuildProject] build project failed, start to fix project")
	err = coding_react.NewAgentV0(a.chatAgent).GenerateCode(ctx, &coding_react.GenerateCodeV0Params{
		ExecutionID:            params.ExecutionID,
		ChatSession:            params.ChatSession,
		AssistantChatMessageID: params.AssistantChatMessageID,
		Project:                params.Project,
		GitRepository:          params.GitRepository,
		UserTask:               buildErrMessage,
		MaxChatTimes:           tcc.GetAgentConfig(ctx).MaxChatTimes,
		MaxLintTimes:           tcc.GetAgentConfig(ctx).MaxLintTimes,
		Options:                &params.Options,
		ChatStage:              common.ChatStage_Building,
		ForbidResponseToUser:   true,
		MaxToolCallTimes:       3,
	})
	if err != nil {
		return false, errorx.Wrapf(err, "method=coding_react.AgentV0.GenerateCode failed")
	}

	return false, nil
}

func (a *AgentImpl) buildProjectForCN(ctx context.Context, params *BuildProjectParams, messageID int64) (isSuccess bool, message *model.Message, err error) {
	startTime := time.Now()
	defer func() {
		success, result := isSuccess, metrics.ProjectBuildResultTagValueSuccess
		if err != nil {
			success, result = false, metrics.ProjectBuildResultTagValueUnknown
			if errors.Is(err, errorx.AgentBuildProjectTimeoutError) {
				result = metrics.ProjectBuildResultTagValueTimeout
			}
		} else if !isSuccess {
			result = metrics.ProjectBuildResultTagValueFailure
		}
		metrics.GetProjectBuilderMetricEmitter(ctx, success, result).EmitMeter(1)
		metrics.GetProjectBuilderMetricEmitter(ctx, success, result).EmitLatency(time.Since(startTime))
	}()

	isSuccess, messageStr, err := file_process.GetProjectBuilderService().BuildForCN(ctx, params.Project.ID)
	if err != nil {
		chatStatus := common.ChatMessageStatus_Failure
		if errors.Is(err, errorx.AgentBuildProjectTimeoutError) {
			chatStatus = common.ChatMessageStatus_Timeout
			logs.CtxWarn(ctx, "[Agent.buildProjectForCN] build project timeout, skip building")
		}
		_, err = chat.GetChatHistoryService().UpdateChatStatus(ctx, &chat.UpdateChatStatusParams{
			ProjectID:      params.Project.ID,
			ChatMessageIDs: []int64{messageID},
			ChatStatus:     chatStatus,
		})
		if err != nil {
			logs.CtxError(ctx, "[Agent.buildProjectForCN] method=ChatHistoryService.UpdateChatStatus failed, messageID=%v, err=%+v", messageID, err)
		}
		return false, nil, errorx.Wrapf(err, "method=BuilderClient.Build failed")
	}
	_, err = chat.GetChatHistoryService().UpdateChatMessage(ctx, &chat.UpdateChatMessageParams{
		ProjectID:      params.Project.ID,
		ChatMessageIDs: []int64{messageID},
		FromStatus:     common.ChatMessageStatusPtr(common.ChatMessageStatus_Processing),
		UpdateStatus:   common.ChatMessageStatusPtr(common.ChatMessageStatus_Completed),
		UpdateMessage: &model.Message{
			Role:    common.MessageRole_User,
			Content: messageStr,
		},
	})
	if err != nil {
		logs.CtxError(ctx, "[Agent.buildProjectForCN] method=ChatHistoryService.UpdateChatStatus failed, messageID=%v, err=%+v", messageID, err)
	}
	if isSuccess {
		return true, nil, nil
	}

	return false, &model.Message{Role: common.MessageRole_User, Content: messageStr}, nil
}

func (a *AgentImpl) buildProjectForI18N(ctx context.Context, params *BuildProjectParams, messageID int64) (isSuccess bool, message *model.Message, err error) {
	startTime := time.Now()
	defer func() {
		success, result := isSuccess, metrics.ProjectBuildResultTagValueSuccess
		if err != nil {
			success, result = false, metrics.ProjectBuildResultTagValueUnknown
			if errors.Is(err, errorx.AgentBuildProjectTimeoutError) {
				result = metrics.ProjectBuildResultTagValueTimeout
			}
		} else if !isSuccess {
			result = metrics.ProjectBuildResultTagValueFailure
		}
		metrics.GetProjectBuilderMetricEmitter(ctx, success, result).EmitMeter(1)
		metrics.GetProjectBuilderMetricEmitter(ctx, success, result).EmitLatency(time.Since(startTime))
	}()

	err = file_process.GetProjectBuilderService().Build(ctx, &file_process.BuildProjectParams{
		ProjectID: params.Project.ID,
		MessageID: messageID,
	})
	if err != nil {
		return false, nil, errorx.Wrapf(err, "method=BuilderClient.Build failed")
	}
	// 启动5分钟定时任务，主动轮训，查看是否build完成，如果超时，则返回
	chatMessage, err := a.retrieveBuildResult(ctx, params.Project.ID, messageID)
	if err != nil {
		return false, nil, errorx.Wrapf(err, "method=Agent.retrieveBuildResult failed")
	}
	if chatMessage == nil || len(chatMessage.GetContent()) <= 0 {
		return true, nil, nil
	}

	return false, chatMessage.Message, nil
}

func (a *AgentImpl) retrieveBuildResult(ctx context.Context, projectID, messageID int64) (message *model.ChatMessage, err error) {
	ticker, timeout := time.NewTicker(5*time.Second), time.After(time.Duration(tcc.GetAgentConfig(ctx).BuildTimeoutSec)*time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			status, err := chat.GetChatHistoryService().GetChatStatus(ctx, &chat.GetChatStatusParams{
				ProjectID:     projectID,
				ChatMessageID: messageID,
			})
			if err != nil {
				logs.CtxError(ctx, "[Agent.BuildProject] method=ChatHistoryService.GetChatStatus failed, err=%+v", err)
				continue
			}
			if status != common.ChatMessageStatus_Completed {
				continue
			}
			message, err = chat.GetChatHistoryService().GetChatMessageByID(ctx, messageID, true)
			if err != nil {
				logs.CtxError(ctx, "[Agent.BuildProject] method=ChatHistoryService.GetChatMessageByID failed, err=%+v", err)
				continue
			}
			return message, nil
		case <-timeout:
			_, err = chat.GetChatHistoryService().UpdateChatStatus(ctx, &chat.UpdateChatStatusParams{
				ProjectID:      projectID,
				ChatMessageIDs: []int64{messageID},
				ChatStatus:     common.ChatMessageStatus_Timeout,
			})
			if err != nil {
				logs.CtxError(ctx, "[Agent.BuildProject] method=ChatHistoryService.UpdateChatStatus failed, messageID=%v, err=%+v", messageID, err)
			}
			logs.CtxWarn(ctx, "[Agent.BuildProject] build project timeout, skip building")
			return nil, errorx.AgentBuildProjectTimeoutError
		}
	}
}
