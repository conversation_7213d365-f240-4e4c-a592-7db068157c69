module code.byted.org/stone/caribou

go 1.23.4

toolchain go1.24.2

require (
	code.byted.org/flow/eino-byted-ext/callbacks/fornax v0.1.4
	code.byted.org/flowdevops/fornax_sdk v1.1.8-beta.2
	code.byted.org/gopkg/env v1.6.18
	code.byted.org/gopkg/lang v0.21.8
	code.byted.org/gopkg/logid v0.0.0-20241008043456-230d03adb830
	code.byted.org/gopkg/logs v1.2.26
	code.byted.org/gopkg/logs/v2 v2.2.0-beta.9
	code.byted.org/gopkg/tos v1.5.9
	code.byted.org/kite/kitutil v3.8.8+incompatible
	code.byted.org/kv/backoff v0.0.0-20191031070508-5d868504e646
	code.byted.org/kv/goredis v5.5.7+incompatible
	code.byted.org/kv/redis-v6 v1.1.2
	code.byted.org/lagrange/viking_go_client v0.0.33
	code.byted.org/middleware/hertz v1.13.7
	code.byted.org/stone/caribou_splitter v0.0.0-20250604111838-72fe029e8f25
	code.byted.org/stone/codedancer_agent v0.0.0-20250516134919-65fccd7055a4
	github.com/JohannesKaufmann/html-to-markdown/v2 v2.3.3
	github.com/Wsine/feishu2md v1.4.0
	github.com/alicebob/miniredis/v2 v2.20.0
	github.com/bytedance/mockey v1.2.14
	github.com/bytedance/sonic v1.13.2
	github.com/chyroc/lark v0.0.113
	github.com/cloudwego/eino v0.3.34
	github.com/cloudwego/eino-ext/components/model/ark v0.1.8
	github.com/cloudwego/eino-ext/components/model/ollama v0.0.0-20250522060253-ddb617598b09
	github.com/cloudwego/eino-ext/components/model/openai v0.0.0-20250519091007-282cc7eb18d3
	github.com/docker/docker v28.1.1+incompatible
	github.com/docker/go-connections v0.5.0
	github.com/getkin/kin-openapi v0.118.0
	github.com/go-git/go-billy/v5 v5.6.2
	github.com/go-git/go-git/v5 v5.13.2
	github.com/go-playground/validator/v10 v10.20.0
	github.com/google/uuid v1.6.0
	github.com/joho/godotenv v1.5.1
	github.com/larksuite/oapi-sdk-go/v3 v3.4.17
	github.com/mark3labs/mcp-go v0.29.0
	github.com/meguminnnnnnnnn/go-openai v0.0.0-20250523041550-e202cd57070c
	github.com/pkg/errors v0.9.1
	github.com/sabhiram/go-gitignore v0.0.0-20210923224102-525f6e181f06
	github.com/smartystreets/goconvey v1.8.1
	github.com/spf13/cobra v1.7.0
	github.com/stretchr/testify v1.10.0
	golang.org/x/sync v0.14.0
	gopkg.in/yaml.v3 v3.0.1
)

require (
	code.byted.org/aiops/apm_vendor_byted v0.0.28 // indirect
	code.byted.org/aiops/metrics_codec v0.0.24 // indirect
	code.byted.org/aiops/monitoring-common-go v0.0.5 // indirect
	code.byted.org/bytedtrace-contrib/kitex-go v1.1.52 // indirect
	code.byted.org/bytedtrace/bytedtrace-client-go v1.2.2 // indirect
	code.byted.org/bytedtrace/bytedtrace-common/go v0.0.13 // indirect
	code.byted.org/bytedtrace/bytedtrace-compatible-lightweight-go v1.0.2 // indirect
	code.byted.org/bytedtrace/bytedtrace-conf-provider-client-go v0.0.27 // indirect
	code.byted.org/bytedtrace/bytedtrace-gls-switch v1.3.0 // indirect
	code.byted.org/bytedtrace/bytedtrace-utils-go v1.0.3 // indirect
	code.byted.org/bytedtrace/interface-go v1.0.20 // indirect
	code.byted.org/bytedtrace/serializer-go v1.0.1-pre // indirect
	code.byted.org/bytees/olivere_elastic/v7 v7.0.34 // indirect
	code.byted.org/data-arch/gotbase v1.0.8-0.20220905113555-b9d46a7dc975 // indirect
	code.byted.org/duanyi.aster/gopkg v0.0.4 // indirect
	code.byted.org/flow/eino v0.3.1 // indirect
	code.byted.org/flow/eino-byted-ext/byted v0.3.13 // indirect
	code.byted.org/flow/eino-byted-ext/callbacks/metrics v0.1.2 // indirect
	code.byted.org/flow/eino-ext/components/model/llmgateway v0.1.2 // indirect
	code.byted.org/flow/flow-telemetry-common/go v0.0.0-20241120062202-3a1bd9cccc33 // indirect
	code.byted.org/flowdevops/errorx v0.0.8-0.20250120073742-b61cdcb8f3df // indirect
	code.byted.org/flowdevops/errorx/code/gen/flow/devops/agent_server v0.0.0-20241012084451-47d6baaffb45 // indirect
	code.byted.org/flowdevops/fornax/pkg/auth v0.0.0-20241211084736-be429d5e6b0c // indirect
	code.byted.org/golf/consul v2.1.13+incompatible // indirect
	code.byted.org/gopkg/apm_vendor_interface v0.0.3 // indirect
	code.byted.org/gopkg/asynccache v0.0.0-20210422090342-26f94f7676b8 // indirect
	code.byted.org/gopkg/consul v1.2.6 // indirect
	code.byted.org/gopkg/context v0.0.1 // indirect
	code.byted.org/gopkg/ctxvalues v0.6.0 // indirect
	code.byted.org/gopkg/debug v0.10.1 // indirect
	code.byted.org/gopkg/etcd_util v2.3.3+incompatible // indirect
	code.byted.org/gopkg/etcdproxy v0.1.1 // indirect
	code.byted.org/gopkg/facility v1.0.14 // indirect
	code.byted.org/gopkg/lang/v2 v2.1.3 // indirect
	code.byted.org/gopkg/localcache v0.9.5 // indirect
	code.byted.org/gopkg/localcache/base v0.8.0 // indirect
	code.byted.org/gopkg/localcache/contributes/freecache v0.7.3 // indirect
	code.byted.org/gopkg/localcache/contributes/gcache v0.8.1 // indirect
	code.byted.org/gopkg/localcache/contributes/vfastcache v0.2.0 // indirect
	code.byted.org/gopkg/metainfo v0.1.4 // indirect
	code.byted.org/gopkg/metrics v1.4.25 // indirect
	code.byted.org/gopkg/metrics/v3 v3.1.35 // indirect
	code.byted.org/gopkg/metrics/v4 v4.1.4 // indirect
	code.byted.org/gopkg/metrics_core v0.0.39 // indirect
	code.byted.org/gopkg/net2 v1.5.0 // indirect
	code.byted.org/gopkg/pkg v0.1.0 // indirect
	code.byted.org/gopkg/retry v0.0.0-20230209024914-cf290f094aa7 // indirect
	code.byted.org/gopkg/stats v1.2.12 // indirect
	code.byted.org/gopkg/tccclient v1.6.0 // indirect
	code.byted.org/gopkg/thrift v1.14.2 // indirect
	code.byted.org/hystrix/hystrix-go v0.0.0-20190214095017-a2a890c81cd5 // indirect
	code.byted.org/inf/authcenter v1.5.2 // indirect
	code.byted.org/inf/infsecc v1.0.3 // indirect
	code.byted.org/inf/sarama v1.5.1 // indirect
	code.byted.org/kite/endpoint v3.7.5+incompatible // indirect
	code.byted.org/kite/kitc v3.10.26+incompatible // indirect
	code.byted.org/kite/kitex v1.18.0 // indirect
	code.byted.org/kite/rpal v0.1.22 // indirect
	code.byted.org/kv/circuitbreaker v0.0.0-20200212034351-d3f51a5b9165 // indirect
	code.byted.org/lang/gg v0.21.0 // indirect
	code.byted.org/lang/trace v0.0.3 // indirect
	code.byted.org/lidar/profiler v0.4.4 // indirect
	code.byted.org/lidar/profiler/hertz v0.4.5 // indirect
	code.byted.org/lidar/profiler/kitex v0.4.5 // indirect
	code.byted.org/log_market/gosdk v0.0.0-20230524072203-e069d8367314 // indirect
	code.byted.org/log_market/loghelper v0.1.11 // indirect
	code.byted.org/log_market/tracelog v0.1.5 // indirect
	code.byted.org/log_market/ttlogagent_gosdk v0.0.7 // indirect
	code.byted.org/log_market/ttlogagent_gosdk/v4 v4.0.54 // indirect
	code.byted.org/middleware/eino v1.0.0-rc.3 // indirect
	code.byted.org/middleware/fic_client v0.2.8 // indirect
	code.byted.org/obric/flow_telemetry_go v1.0.6 // indirect
	code.byted.org/overpass/data_aml_llmflow_engine v0.0.0-20241107145550-f2da45272e96 // indirect
	code.byted.org/overpass/stone_llm_gateway v0.0.0-20241209123631-77cf7aa60e81 // indirect
	code.byted.org/security/go-spiffe-v2 v1.0.8 // indirect
	code.byted.org/security/memfd v0.0.2 // indirect
	code.byted.org/security/sensitive_finder_engine v0.3.18 // indirect
	code.byted.org/security/zti-jwt-helper-golang v1.0.18 // indirect
	code.byted.org/service_mesh/shmipc v0.2.16 // indirect
	code.byted.org/tiktok/buildinfo v0.0.2 // indirect
	code.byted.org/toutiao/elastic/v7 v7.0.42 // indirect
	code.byted.org/trace/trace-client-go v1.3.7 // indirect
	code.byted.org/videoarch/vfastcache v1.0.10 // indirect
	code.byted.org/webcast/libs_anycache v1.6.7 // indirect
	code.byted.org/webcast/libs_anycache/plugin/cache/base v0.1.1-0.20221212082232-7c36e6844ac9 // indirect
	code.byted.org/webcast/libs_anycache/plugin/cache/objectcache v0.0.1 // indirect
	code.byted.org/webcast/libs_anycache/plugin/codec/base v0.1.0 // indirect
	code.byted.org/webcast/libs_anycache/plugin/refresh v0.1.3 // indirect
	code.byted.org/webcast/libs_sync v0.1.2 // indirect
	dario.cat/mergo v1.0.0 // indirect
	github.com/JohannesKaufmann/dom v0.2.0 // indirect
	github.com/Knetic/govaluate v3.0.1-0.20171022003610-9aa49832a739+incompatible // indirect
	github.com/Microsoft/go-winio v0.6.1 // indirect
	github.com/ProtonMail/go-crypto v1.1.5 // indirect
	github.com/alicebob/gopher-json v0.0.0-20230218143504-906a9b012302 // indirect
	github.com/antlr4-go/antlr/v4 v4.13.0 // indirect
	github.com/apache/thrift v0.19.0 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bits-and-blooms/bitset v1.13.0 // indirect
	github.com/bits-and-blooms/bloom/v3 v3.6.0 // indirect
	github.com/bluele/gcache v0.0.2 // indirect
	github.com/bufbuild/protocompile v0.8.0 // indirect
	github.com/bytedance/gopkg v0.1.1 // indirect
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/caarlos0/env/v6 v6.10.1 // indirect
	github.com/cenk/backoff v2.2.1+incompatible // indirect
	github.com/cenkalti/backoff/v4 v4.3.0 // indirect
	github.com/cespare/xxhash v1.1.0 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/cloudflare/circl v1.3.7 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/cloudwego/configmanager v0.2.2 // indirect
	github.com/cloudwego/dynamicgo v0.4.6 // indirect
	github.com/cloudwego/eino-ext/libs/acl/openai v0.0.0-20250519084852-38fafa73d9ea // indirect
	github.com/cloudwego/fastpb v0.0.5 // indirect
	github.com/cloudwego/frugal v0.2.3 // indirect
	github.com/cloudwego/gopkg v0.1.3 // indirect
	github.com/cloudwego/hertz v0.9.6 // indirect
	github.com/cloudwego/iasm v0.2.0 // indirect
	github.com/cloudwego/kitex v0.12.0 // indirect
	github.com/cloudwego/kitex/pkg/protocol/bthrift v0.0.0-20241121032620-4e238714e458 // indirect
	github.com/cloudwego/localsession v0.1.1 // indirect
	github.com/cloudwego/netpoll v0.6.5 // indirect
	github.com/cloudwego/runtimex v0.1.0 // indirect
	github.com/cloudwego/thriftgo v0.3.18 // indirect
	github.com/containerd/log v0.1.0 // indirect
	github.com/coocood/freecache v1.2.0 // indirect
	github.com/cyphar/filepath-securejoin v0.3.6 // indirect
	github.com/danielgtaylor/huma v1.14.2 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/distribution/reference v0.6.0 // indirect
	github.com/dlclark/regexp2 v1.11.4 // indirect
	github.com/docker/go-units v0.5.0 // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/eapache/go-resiliency v1.2.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20180814174437-776d5712da21 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/emirpasic/gods v1.18.1 // indirect
	github.com/evanphx/json-patch v0.5.2 // indirect
	github.com/facebookgo/clock v0.0.0-20150410010913-600d898af40a // indirect
	github.com/fatih/structtag v1.2.0 // indirect
	github.com/felixge/httpsnoop v1.0.4 // indirect
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/gabriel-vasile/mimetype v1.4.3 // indirect
	github.com/go-git/gcfg v1.5.1-0.20230307220236-3a3c6141e376 // indirect
	github.com/go-jose/go-jose/v3 v3.0.3 // indirect
	github.com/go-kit/log v0.2.1 // indirect
	github.com/go-logfmt/logfmt v0.6.0 // indirect
	github.com/go-logr/logr v1.4.2 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-ole/go-ole v1.3.0 // indirect
	github.com/go-openapi/jsonpointer v0.21.0 // indirect
	github.com/go-openapi/swag v0.23.0 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-task/slim-sprig v0.0.0-20230315185526-52ccab3ef572 // indirect
	github.com/go-task/slim-sprig/v3 v3.0.0 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang-jwt/jwt/v5 v5.2.1 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/martian v2.1.0+incompatible // indirect
	github.com/google/pprof v0.0.0-20240727154555-813a5fbdbec8 // indirect
	github.com/goph/emperror v0.17.2 // indirect
	github.com/gopherjs/gopherjs v1.17.2 // indirect
	github.com/gorilla/mux v1.8.1 // indirect
	github.com/hashicorp/hcl v1.0.1-0.20190430135223-99e2f22d1c94 // indirect
	github.com/hbollon/go-edlib v1.6.0 // indirect
	github.com/hertz-contrib/http2 v0.1.8 // indirect
	github.com/hertz-contrib/localsession v0.1.0 // indirect
	github.com/iancoleman/strcase v0.3.0 // indirect
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/invopop/yaml v0.3.1 // indirect
	github.com/jbenet/go-context v0.0.0-20150711004518-d14ea06fba99 // indirect
	github.com/jhump/protoreflect v1.15.6 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/jtolds/gls v4.20.0+incompatible // indirect
	github.com/kevinburke/ssh_config v1.2.0 // indirect
	github.com/klauspost/compress v1.17.10 // indirect
	github.com/klauspost/cpuid/v2 v2.2.9 // indirect
	github.com/klauspost/crc32 v1.2.0 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/magiconair/properties v1.8.7 // indirect
	github.com/mailru/easyjson v0.9.0 // indirect
	github.com/mattn/go-pointer v0.0.1 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/moby/docker-image-spec v1.3.1 // indirect
	github.com/moby/sys/atomicwriter v0.1.0 // indirect
	github.com/moby/term v0.5.2 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/gls v0.0.0-20220109145502-612d0167dce5 // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/morikuni/aec v1.0.0 // indirect
	github.com/nikolalohinski/gonja v1.5.3 // indirect
	github.com/nikolalohinski/gonja/v2 v2.3.1 // indirect
	github.com/nyaruka/phonenumbers v1.3.2 // indirect
	github.com/olivere/elastic/v7 v7.0.32 // indirect
	github.com/ollama/ollama v0.5.12 // indirect
	github.com/opencontainers/go-digest v1.0.0 // indirect
	github.com/opencontainers/image-spec v1.1.1 // indirect
	github.com/opentracing/opentracing-go v1.2.1-0.20210726034734-bdbb7cc3a1c0 // indirect
	github.com/pelletier/go-toml/v2 v2.2.3 // indirect
	github.com/perimeterx/marshmallow v1.1.5 // indirect
	github.com/philhofer/fwd v1.1.1 // indirect
	github.com/pierrec/lz4/v4 v4.1.21 // indirect
	github.com/pjbgf/sha1cd v0.3.2 // indirect
	github.com/pkoukk/tiktoken-go v0.1.7 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/power-devops/perfstat v0.0.0-20240221224432-82ca36839d55 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/rivo/uniseg v0.4.7 // indirect
	github.com/sagikazarmark/locafero v0.4.0 // indirect
	github.com/sagikazarmark/slog-shim v0.1.0 // indirect
	github.com/sashabaranov/go-openai v1.37.0 // indirect
	github.com/sergi/go-diff v1.3.2-0.20230802210424-5b0b94c5c0d3 // indirect
	github.com/shirou/gopsutil/v3 v3.24.2 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	github.com/skeema/knownhosts v1.3.0 // indirect
	github.com/slongfield/pyfmt v0.0.0-20220222012616-ea85ff4c361f // indirect
	github.com/smarty/assertions v1.15.1 // indirect
	github.com/sourcegraph/conc v0.3.0 // indirect
	github.com/spf13/afero v1.11.0 // indirect
	github.com/spf13/cast v1.7.1 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/spf13/viper v1.18.2 // indirect
	github.com/subosito/gotenv v1.6.0 // indirect
	github.com/tidwall/gjson v1.17.3 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.1 // indirect
	github.com/tinylib/msgp v1.1.6 // indirect
	github.com/tree-sitter-grammars/tree-sitter-yaml v0.7.1 // indirect
	github.com/tree-sitter/go-tree-sitter v0.25.0 // indirect
	github.com/tree-sitter/tree-sitter-c v0.23.4 // indirect
	github.com/tree-sitter/tree-sitter-cpp v0.23.4 // indirect
	github.com/tree-sitter/tree-sitter-css v0.23.2 // indirect
	github.com/tree-sitter/tree-sitter-go v0.23.4 // indirect
	github.com/tree-sitter/tree-sitter-html v0.23.2 // indirect
	github.com/tree-sitter/tree-sitter-java v0.23.5 // indirect
	github.com/tree-sitter/tree-sitter-javascript v0.23.1 // indirect
	github.com/tree-sitter/tree-sitter-json v0.24.8 // indirect
	github.com/tree-sitter/tree-sitter-python v0.23.6 // indirect
	github.com/tree-sitter/tree-sitter-rust v0.23.2 // indirect
	github.com/tree-sitter/tree-sitter-typescript v0.23.2 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/twmb/murmur3 v1.1.8 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasttemplate v1.2.2 // indirect
	github.com/vmihailenco/msgpack v4.0.4+incompatible // indirect
	github.com/vmihailenco/msgpack/v4 v4.3.12 // indirect
	github.com/vmihailenco/tagparser v0.1.2 // indirect
	github.com/volcengine/volc-sdk-golang v1.0.196 // indirect
	github.com/volcengine/volcengine-go-sdk v1.1.4 // indirect
	github.com/xanzy/ssh-agent v0.3.3 // indirect
	github.com/yargevad/filepathx v1.0.0 // indirect
	github.com/yosida95/uritemplate/v3 v3.0.2 // indirect
	github.com/yuin/goldmark v1.7.12 // indirect
	github.com/yuin/gopher-lua v1.1.1 // indirect
	github.com/yusufpapurcu/wmi v1.2.4 // indirect
	github.com/zeebo/errs v1.4.0 // indirect
	go.opentelemetry.io/auto/sdk v1.1.0 // indirect
	go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.61.0 // indirect
	go.opentelemetry.io/otel v1.36.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp v1.36.0 // indirect
	go.opentelemetry.io/otel/metric v1.36.0 // indirect
	go.opentelemetry.io/otel/trace v1.36.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	go4.org/unsafe/assume-no-moving-gc v0.0.0-20231121144256-b99613f794b6 // indirect
	golang.org/x/arch v0.14.0 // indirect
	golang.org/x/crypto v0.38.0 // indirect
	golang.org/x/exp v0.0.0-20250218142911-aa4b98e5adaa // indirect
	golang.org/x/mod v0.23.0 // indirect
	golang.org/x/net v0.40.0 // indirect
	golang.org/x/sys v0.33.0 // indirect
	golang.org/x/text v0.25.0 // indirect
	golang.org/x/time v0.5.0 // indirect
	golang.org/x/tools v0.30.0 // indirect
	google.golang.org/appengine v1.6.8 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20250519155744-55703ea1f237 // indirect
	google.golang.org/grpc v1.72.1 // indirect
	google.golang.org/protobuf v1.36.6 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/vmihailenco/msgpack.v2 v2.9.2 // indirect
	gopkg.in/warnings.v0 v0.1.2 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
)

replace github.com/apache/thrift => github.com/apache/thrift v0.13.0
