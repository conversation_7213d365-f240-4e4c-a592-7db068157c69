*.o
*.a
*.so
_obj
_test
*.[568vq]
[568vq].out
*.cgo1.go
*.cgo2.c
_cgo_defun.c
_cgo_gotypes.go
_cgo_export.*
_testmain.go
*.exe
*.exe~
*.test
*.prof
*.rar
*.zip
*.gz
*.psd
*.bmd
*.cfg
*.pptx
*.log
*nohup.out
*settings.pyc
*.sublime-project
*.sublime-workspace
!.gitkeep
.DS_Store
/codedancer_agent/.idea
/.vscode
/output
/cmd/evaluation/output/*
modules/agents/*/cmd/*
*.local.yml
dumped_hertz_remote_config.json
/modules/prompt_platform_session_loader/output
cover-files/
c.out
/conf/kitex_remote_config.json
.cursor/
bin/caribou-darwin
bin/caribou-linux
bin/mcp-demo-cli
cmd/gen_klg/main
cmd/gen_klg/gen_klg
cmd/gen_tutorial/gen_tutorial
cmd/klg_cli/klg_cli
cmd/klg_test/read_doc
cmd/klg_test/search_klg
cmd/klg_cli/*.md
cmd/klg_test/.cache/
cmd/gen_klg/klg_base/
cmd/klg_cli/klg_base/
cmd/klg_cli/klg_cli
cmd/klg_cli/query.list
cmd/klg_cli/*.txt
cmd/klg_cli/*.md
cmd/search_klg/search_klg
.cache/
cmd/klg_test/.cache/
cmd/klg_test/read_doc
cmd/klg_test/search_klg
cmd/gen_tutorial/repoDir/
modules/agents/tutorial_agent/repoDir/
cmd/gen_tutorial/gen_tutorial
cmd/gen_tutorial/readmeDir/
