#!/usr/bin/env node

import { z } from 'zod';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';

const defaultKnowledgeApiUrl = 'https://8drq6s8d.fn.bytedance.net/api/knowledge/search';
const KNOWLEDGE_API_URL = process.env.KNOWLEDGE_API_URL || defaultKnowledgeApiUrl;

const server = new McpServer({
  name: 'caribou-knowledge-mcp-server',
  version: '1.0.0',
});

// 注册搜索知识库的处理器
server.tool(
  'search-bytedance-knowledge',
  '搜索 Bytedance 内场知识',
  {
    query: z.string().describe('搜索关键词').min(1),
  },
  async (params: { query: string }) => {
    try {
      const timeout = 60 * 1000;
      const response = await fetch(`${KNOWLEDGE_API_URL}?query=${params.query}`, {
        signal: AbortSignal.timeout(timeout),
      });

      const res = await response.json();

      if (res.code !== 0 || !Array.isArray(res.data)) {
        throw new Error(`[${res.code}] ${res.message}`);
      }

      const chunks = res.data.map((it: Record<string, string>) => {
        const { Content = '' } = it || {};
        return {
          type: 'text',
          text: Content,
        }
      }).filter((it: { text: string }) => it?.text);

      if (chunks.length === 0) {
        chunks.push({
          type: 'text',
          text: '未找到相关知识',
        })
      }

      return {
        content: chunks,
      }
    } catch (error) {
      console.error('搜索知识库失败:', error);
      return {
        content: [
          {
            type: 'text',
            text: '搜索知识库失败'
          },
          {
            type: 'text',
            text: error instanceof Error ? error.message : '未知错误'
          }
        ],
      };
    }
  });

// 启动 MCP 服务器
async function main() {
  try {
    const transport = new StdioServerTransport();
    await server.connect(transport);
    console.error('Caribou Knowledge MCP Server running on stdio');
  } catch (error) {
    console.error('Caribou Knowledge MCP Server failed to start:', error);
    process.exit(1);
  }
}

void main();
