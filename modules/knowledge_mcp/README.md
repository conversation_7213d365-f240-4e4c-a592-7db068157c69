# Caribou Knowledge MCP Server

这是一个基于 MCP (Model Context Protocol) 协议的知识库搜索服务。

## 使用方法

Cursor 配置(`~/.cursor/mcp.json`)：

```json
{
  "mcpServers": {
    "caribou-knowledge": {
      "command": "npx",
      "args": [
        "-y",
        "@byted/caribou-knowledge-mcp-server"
      ]
    }
  }
}
```

## 开发

### 本地开发

1. 安装依赖：

```bash
pnpm install
```

2. 运行服务：

```bash
pnpm start
```

3. 调试服务：

```sh
pnpm dev
pnpm dev:mcp
```
