{"name": "@byted/caribou-knowledge-mcp-server", "version": "1.0.3", "description": "Caribou Knowledge MCP Server", "main": "dist/index.js", "type": "module", "scripts": {"build": "rm -rf dist && tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "dev:mcp": "npm run build &&npx @modelcontextprotocol/inspector pnpm start", "watch": "tsc -w", "prepublishOnly": "npm run build"}, "bin": {"caribou-knowledge": "dist/index.js"}, "engines": {"node": ">=16.0.0"}, "files": ["dist", "README.md"], "dependencies": {"@modelcontextprotocol/sdk": "^1.0.3", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.11.28", "ts-node": "^10.9.2", "typescript": "^5.4.2"}}