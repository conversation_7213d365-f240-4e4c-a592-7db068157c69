package util

import (
	"net/http"

	"code.byted.org/middleware/hertz/pkg/app"
)

type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

const (
	SuccessCode        = 0
	InvalidRequestCode = 400
	RecordNotFoundCode = 404
	ServerErrorCode    = 500
)

func Success(data interface{}) *Response {
	return &Response{Code: 0, Message: "success", Data: data}
}

func Error(code int, message string) *Response {
	return &Response{Code: code, Message: message, Data: nil}
}

func HertzResponseSuccess(c *app.RequestContext, data interface{}) {
	c.JSON(http.StatusOK, &Response{Code: 0, Message: "success", Data: data})
}

func HertzResponseError(c *app.RequestContext, code int, message string) {
	c.JSON(http.StatusOK, &Response{Code: code, Message: message, Data: nil})
}
