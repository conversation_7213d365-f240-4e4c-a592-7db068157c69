#!/bin/bash
RUN_NAME="stone.caribou.knowledge"

# fix gcc error
export CGO_ENABLED=0

WORK_DIR=modules/knowledge_server

mkdir -p output/bin output/conf
cp ${WORK_DIR}/script/bootstrap.sh output 2>/dev/null
chmod +x output/bootstrap.sh
cp ${WORK_DIR}/script/bootstrap.sh output/bootstrap_staging.sh
chmod +x output/bootstrap_staging.sh
cp ${WORK_DIR}/script/run.sh output/run.sh
chmod +x output/run.sh

find ${WORK_DIR}/conf/ -type f ! -name "*_local.*" | xargs -I{} cp {} output/conf/

cd ${WORK_DIR}
go build -o ../../output/bin/${RUN_NAME}