package service

import (
	"context"
	"encoding/json"

	"code.byted.org/stone/caribou/modules/agents/tools/knowledge"
	"github.com/cloudwego/eino/components/tool"
)

type KnowledgeToolService interface {
	SearchKnowledge(ctx context.Context, query string) ([]*knowledge.Knowledge, error)
}

type KnowledgeToolServiceImpl struct {
	knowledgeQueryTool tool.InvokableTool
}

var defaultKnowledgeToolService KnowledgeToolService

func NewKnowledgeToolService(knowledgeQueryTool tool.InvokableTool) KnowledgeToolService {
	return &KnowledgeToolServiceImpl{
		knowledgeQueryTool: knowledgeQueryTool,
	}
}

func GetKnowledgeToolService() KnowledgeToolService {
	return defaultKnowledgeToolService
}

func MustInitKnowledgeToolService() KnowledgeToolService {
	defaultKnowledgeToolService = NewKnowledgeToolService(knowledge.GetKnowledgeQueryTool())
	return defaultKnowledgeToolService
}

func (s *KnowledgeToolServiceImpl) SearchKnowledge(ctx context.Context, query string) ([]*knowledge.Knowledge, error) {
	serializeQuery, err := json.Marshal(&knowledge.QueryKnowledgeParams{
		Query: query,
	})
	if err != nil {
		return nil, err
	}

	resp, err := s.knowledgeQueryTool.InvokableRun(ctx, string(serializeQuery))
	if err != nil {
		return nil, err
	}

	chunks := make([]*knowledge.Knowledge, 0)
	if err := json.Unmarshal([]byte(resp), &chunks); err != nil {
		return nil, err
	}

	return chunks, nil
}
