package handler

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/middleware/hertz/pkg/app"
	knowledgeToolService "code.byted.org/stone/caribou/modules/knowledge_server/internal/domain/knowledge_tool/service"
	"code.byted.org/stone/caribou/modules/knowledge_server/pkg/util"
)

func SearchKnowledge(ctx context.Context, c *app.RequestContext) {
	query := c.Query("query")

	content, err := knowledgeToolService.GetKnowledgeToolService().SearchKnowledge(ctx, query)
	if err != nil {
		logs.CtxError(ctx, "[Hnadler=SearchKnowledge] search knowledge failed, query: %s, err: %s", query, err)
		util.HertzResponseError(c, util.ServerErrorCode, err.Error())
		return
	}
	util.HertzResponseSuccess(c, content)
}
