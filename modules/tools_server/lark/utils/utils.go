package utils

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"reflect"
	"strings"

	"code.byted.org/stone/caribou/modules/tools_server/conf"
	base_utils "code.byted.org/stone/caribou/modules/tools_server/utils"
	"github.com/Wsine/feishu2md/utils"
	"github.com/chyroc/lark"
	larkSdk "github.com/larksuite/oapi-sdk-go/v3"
	larkdocx "github.com/larksuite/oapi-sdk-go/v3/service/docx/v1"
)

type Parser struct {
	useHTMLTags bool
	ImgTokens   []string
	blockMap    map[string]*larkdocx.Block
}

func NewParser() *Parser {
	return &Parser{
		useHTMLTags: true,
		ImgTokens:   make([]string, 0),
		blockMap:    make(map[string]*larkdocx.Block),
	}
}

// =============================================================
// Parser utils
// =============================================================

const (
	DocxBlockTypePage           int = 1   // 文档 Block
	DocxBlockTypeText           int = 2   // 文本 Block
	DocxBlockTypeHeading1       int = 3   // 一级标题 Block
	DocxBlockTypeHeading2       int = 4   // 二级标题 Block
	DocxBlockTypeHeading3       int = 5   // 三级标题 Block
	DocxBlockTypeHeading4       int = 6   // 四级标题 Block
	DocxBlockTypeHeading5       int = 7   // 五级标题 Block
	DocxBlockTypeHeading6       int = 8   // 六级标题 Block
	DocxBlockTypeHeading7       int = 9   // 七级标题 Block
	DocxBlockTypeHeading8       int = 10  // 八级标题 Block
	DocxBlockTypeHeading9       int = 11  // 九级标题 Block
	DocxBlockTypeBullet         int = 12  // 无序列表 Block
	DocxBlockTypeOrdered        int = 13  // 有序列表 Block
	DocxBlockTypeCode           int = 14  // 代码块 Block
	DocxBlockTypeQuote          int = 15  // 引用 Block
	DocxBlockTypeEquation       int = 16  // 公式 Block
	DocxBlockTypeTodo           int = 17  // 任务 Block
	DocxBlockTypeBitable        int = 18  // 多维表格 Block
	DocxBlockTypeCallout        int = 19  // 高亮块 Block
	DocxBlockTypeChatCard       int = 20  // 群聊卡片 Block
	DocxBlockTypeDiagram        int = 21  // 流程图/UML Block
	DocxBlockTypeDivider        int = 22  // 分割线 Block
	DocxBlockTypeFile           int = 23  // 文件 Block
	DocxBlockTypeGrid           int = 24  // 分栏 Block
	DocxBlockTypeGridColumn     int = 25  // 分栏列 Block
	DocxBlockTypeIframe         int = 26  // 内嵌 Block
	DocxBlockTypeImage          int = 27  // 图片 Block
	DocxBlockTypeISV            int = 28  // 三方 Block
	DocxBlockTypeMindnote       int = 29  // 思维笔记 Block
	DocxBlockTypeSheet          int = 30  // 电子表格 Block
	DocxBlockTypeTable          int = 31  // 表格 Block
	DocxBlockTypeTableCell      int = 32  // 单元格 Block
	DocxBlockTypeView           int = 33  // 视图 Block
	DocxBlockTypeQuoteContainer int = 34  // 引用容器 Block
	DocxBlockTypeTask           int = 35  // 任务容器 Block
	DocxBlockTypeOKR            int = 36  // OKR容器 Block
	DocxBlockTypeOKRObjective   int = 37  // OKR Objective容器 Block
	DocxBlockTypeOKRKeyResult   int = 38  // OKR KeyResult容器 Block
	DocxBlockTypeProgress       int = 39  // Progress容器 Block
	DocxBlockTypeUndefined      int = 999 // 未支持 Block
)

var DocxCodeLang2MdStr = map[int]string{
	DocxCodeLanguagePlainText:    "",
	DocxCodeLanguageABAP:         "abap",
	DocxCodeLanguageAda:          "ada",
	DocxCodeLanguageApache:       "apache",
	DocxCodeLanguageApex:         "apex",
	DocxCodeLanguageAssembly:     "assembly",
	DocxCodeLanguageBash:         "bash",
	DocxCodeLanguageCSharp:       "csharp",
	DocxCodeLanguageCPlusPlus:    "cpp",
	DocxCodeLanguageC:            "c",
	DocxCodeLanguageCOBOL:        "cobol",
	DocxCodeLanguageCSS:          "css",
	DocxCodeLanguageCoffeeScript: "coffeescript",
	DocxCodeLanguageD:            "d",
	DocxCodeLanguageDart:         "dart",
	DocxCodeLanguageDelphi:       "delphi",
	DocxCodeLanguageDjango:       "django",
	DocxCodeLanguageDockerfile:   "dockerfile",
	DocxCodeLanguageErlang:       "erlang",
	DocxCodeLanguageFortran:      "fortran",
	DocxCodeLanguageFoxPro:       "foxpro",
	DocxCodeLanguageGo:           "go",
	DocxCodeLanguageGroovy:       "groovy",
	DocxCodeLanguageHTML:         "html",
	DocxCodeLanguageHTMLBars:     "htmlbars",
	DocxCodeLanguageHTTP:         "http",
	DocxCodeLanguageHaskell:      "haskell",
	DocxCodeLanguageJSON:         "json",
	DocxCodeLanguageJava:         "java",
	DocxCodeLanguageJavaScript:   "javascript",
	DocxCodeLanguageJulia:        "julia",
	DocxCodeLanguageKotlin:       "kotlin",
	DocxCodeLanguageLateX:        "latex",
	DocxCodeLanguageLisp:         "lisp",
	DocxCodeLanguageLogo:         "logo",
	DocxCodeLanguageLua:          "lua",
	DocxCodeLanguageMATLAB:       "matlab",
	DocxCodeLanguageMakefile:     "makefile",
	DocxCodeLanguageMarkdown:     "markdown",
	DocxCodeLanguageNginx:        "nginx",
	DocxCodeLanguageObjective:    "objectivec",
	DocxCodeLanguageOpenEdgeABL:  "openedge-abl",
	DocxCodeLanguagePHP:          "php",
	DocxCodeLanguagePerl:         "perl",
	DocxCodeLanguagePostScript:   "postscript",
	DocxCodeLanguagePower:        "powershell",
	DocxCodeLanguageProlog:       "prolog",
	DocxCodeLanguageProtoBuf:     "protobuf",
	DocxCodeLanguagePython:       "python",
	DocxCodeLanguageR:            "r",
	DocxCodeLanguageRPG:          "rpg",
	DocxCodeLanguageRuby:         "ruby",
	DocxCodeLanguageRust:         "rust",
	DocxCodeLanguageSAS:          "sas",
	DocxCodeLanguageSCSS:         "scss",
	DocxCodeLanguageSQL:          "sql",
	DocxCodeLanguageScala:        "scala",
	DocxCodeLanguageScheme:       "scheme",
	DocxCodeLanguageScratch:      "scratch",
	DocxCodeLanguageShell:        "shell",
	DocxCodeLanguageSwift:        "swift",
	DocxCodeLanguageThrift:       "thrift",
	DocxCodeLanguageTypeScript:   "typescript",
	DocxCodeLanguageVBScript:     "vbscript",
	DocxCodeLanguageVisual:       "vbnet",
	DocxCodeLanguageXML:          "xml",
	DocxCodeLanguageYAML:         "yaml",
}

const (
	DocxCodeLanguagePlainText    int = 1  // PlainText
	DocxCodeLanguageABAP         int = 2  // ABAP
	DocxCodeLanguageAda          int = 3  // Ada
	DocxCodeLanguageApache       int = 4  // Apache
	DocxCodeLanguageApex         int = 5  // Apex
	DocxCodeLanguageAssembly     int = 6  // Assembly
	DocxCodeLanguageBash         int = 7  // Bash
	DocxCodeLanguageCSharp       int = 8  // CSharp
	DocxCodeLanguageCPlusPlus    int = 9  // C++
	DocxCodeLanguageC            int = 10 // C
	DocxCodeLanguageCOBOL        int = 11 // COBOL
	DocxCodeLanguageCSS          int = 12 // CSS
	DocxCodeLanguageCoffeeScript int = 13 // CoffeeScript
	DocxCodeLanguageD            int = 14 // D
	DocxCodeLanguageDart         int = 15 // Dart
	DocxCodeLanguageDelphi       int = 16 // Delphi
	DocxCodeLanguageDjango       int = 17 // Django
	DocxCodeLanguageDockerfile   int = 18 // Dockerfile
	DocxCodeLanguageErlang       int = 19 // Erlang
	DocxCodeLanguageFortran      int = 20 // Fortran
	DocxCodeLanguageFoxPro       int = 21 // FoxPro
	DocxCodeLanguageGo           int = 22 // Go
	DocxCodeLanguageGroovy       int = 23 // Groovy
	DocxCodeLanguageHTML         int = 24 // HTML
	DocxCodeLanguageHTMLBars     int = 25 // HTMLBars
	DocxCodeLanguageHTTP         int = 26 // HTTP
	DocxCodeLanguageHaskell      int = 27 // Haskell
	DocxCodeLanguageJSON         int = 28 // JSON
	DocxCodeLanguageJava         int = 29 // Java
	DocxCodeLanguageJavaScript   int = 30 // JavaScript
	DocxCodeLanguageJulia        int = 31 // Julia
	DocxCodeLanguageKotlin       int = 32 // Kotlin
	DocxCodeLanguageLateX        int = 33 // LateX
	DocxCodeLanguageLisp         int = 34 // Lisp
	DocxCodeLanguageLogo         int = 35 // Logo
	DocxCodeLanguageLua          int = 36 // Lua
	DocxCodeLanguageMATLAB       int = 37 // MATLAB
	DocxCodeLanguageMakefile     int = 38 // Makefile
	DocxCodeLanguageMarkdown     int = 39 // Markdown
	DocxCodeLanguageNginx        int = 40 // Nginx
	DocxCodeLanguageObjective    int = 41 // Objective
	DocxCodeLanguageOpenEdgeABL  int = 42 // OpenEdgeABL
	DocxCodeLanguagePHP          int = 43 // PHP
	DocxCodeLanguagePerl         int = 44 // Perl
	DocxCodeLanguagePostScript   int = 45 // PostScript
	DocxCodeLanguagePower        int = 46 // Power
	DocxCodeLanguageProlog       int = 47 // Prolog
	DocxCodeLanguageProtoBuf     int = 48 // ProtoBuf
	DocxCodeLanguagePython       int = 49 // Python
	DocxCodeLanguageR            int = 50 // R
	DocxCodeLanguageRPG          int = 51 // RPG
	DocxCodeLanguageRuby         int = 52 // Ruby
	DocxCodeLanguageRust         int = 53 // Rust
	DocxCodeLanguageSAS          int = 54 // SAS
	DocxCodeLanguageSCSS         int = 55 // SCSS
	DocxCodeLanguageSQL          int = 56 // SQL
	DocxCodeLanguageScala        int = 57 // Scala
	DocxCodeLanguageScheme       int = 58 // Scheme
	DocxCodeLanguageScratch      int = 59 // Scratch
	DocxCodeLanguageShell        int = 60 // Shell
	DocxCodeLanguageSwift        int = 61 // Swift
	DocxCodeLanguageThrift       int = 62 // Thrift
	DocxCodeLanguageTypeScript   int = 63 // TypeScript
	DocxCodeLanguageVBScript     int = 64 // VBScript
	DocxCodeLanguageVisual       int = 65 // Visual
	DocxCodeLanguageXML          int = 66 // XML
	DocxCodeLanguageYAML         int = 67 // YAML
)

// =============================================================
// Parse the new version of document (docx)
// =============================================================

func (p *Parser) ParseDocxContent(docToken string, blocks []*larkdocx.Block) string {
	for _, block := range blocks {
		p.blockMap[*block.BlockId] = block
	}

	entryBlock := p.blockMap[docToken]
	if entryBlock == nil {
		return fmt.Sprintf("ParseDocxContent error: entry block for docToken %s not found", docToken)
	}
	return p.ParseDocxBlock(entryBlock, 0)
}

func (p *Parser) ParseDocxBlock(b *larkdocx.Block, indentLevel int) string {
	if b == nil {
		return ""
	}
	buf := new(strings.Builder)
	buf.WriteString(strings.Repeat("\t", indentLevel))
	if b.BlockType == nil {
		return buf.String()
	}
	switch *b.BlockType {
	case DocxBlockTypePage:
		buf.WriteString(p.ParseDocxBlockPage(b))
	case DocxBlockTypeText:
		buf.WriteString(p.ParseDocxBlockText(b.Text))
	case DocxBlockTypeCallout:
		buf.WriteString(p.ParseDocxBlockCallout(b))
	case DocxBlockTypeHeading1:
		buf.WriteString(p.ParseDocxBlockHeading(b, 1))
	case DocxBlockTypeHeading2:
		buf.WriteString(p.ParseDocxBlockHeading(b, 2))
	case DocxBlockTypeHeading3:
		buf.WriteString(p.ParseDocxBlockHeading(b, 3))
	case DocxBlockTypeHeading4:
		buf.WriteString(p.ParseDocxBlockHeading(b, 4))
	case DocxBlockTypeHeading5:
		buf.WriteString(p.ParseDocxBlockHeading(b, 5))
	case DocxBlockTypeHeading6:
		buf.WriteString(p.ParseDocxBlockHeading(b, 6))
	case DocxBlockTypeHeading7:
		buf.WriteString(p.ParseDocxBlockHeading(b, 7))
	case DocxBlockTypeHeading8:
		buf.WriteString(p.ParseDocxBlockHeading(b, 8))
	case DocxBlockTypeHeading9:
		buf.WriteString(p.ParseDocxBlockHeading(b, 9))
	case DocxBlockTypeBullet:
		buf.WriteString(p.ParseDocxBlockBullet(b, indentLevel))
	case DocxBlockTypeOrdered:
		buf.WriteString(p.ParseDocxBlockOrdered(b, indentLevel))
	case DocxBlockTypeCode:
		if b.Code != nil && b.Code.Style != nil && b.Code.Style.Language != nil {
			buf.WriteString("```" + DocxCodeLang2MdStr[*b.Code.Style.Language] + "\n")
			buf.WriteString(strings.TrimSpace(p.ParseDocxBlockText(b.Code)))
			buf.WriteString("\n```\n")
		}
	case DocxBlockTypeQuote:
		if b.Quote != nil {
			buf.WriteString("> ")
			buf.WriteString(p.ParseDocxBlockText(b.Quote))
		}
	case DocxBlockTypeEquation:
		if b.Equation != nil {
			buf.WriteString("$$\n")
			buf.WriteString(p.ParseDocxBlockText(b.Equation))
			buf.WriteString("\n$$\n")
		}
	case DocxBlockTypeTodo:
		if b.Todo != nil && b.Todo.Style != nil && b.Todo.Style.Done != nil {
			if *b.Todo.Style.Done {
				buf.WriteString("- [x] ")
			} else {
				buf.WriteString("- [ ] ")
			}
			buf.WriteString(p.ParseDocxBlockText(b.Todo))
		}
	case DocxBlockTypeDivider:
		buf.WriteString("---\n")
	case DocxBlockTypeImage:
		if b.Image != nil {
			buf.WriteString(p.ParseDocxBlockImage(b.Image))
		}
	case DocxBlockTypeTableCell:
		buf.WriteString(p.ParseDocxBlockTableCell(b))
	case DocxBlockTypeTable:
		if b.Table != nil {
			buf.WriteString(p.ParseDocxBlockTable(b.Table))
		}
	case DocxBlockTypeQuoteContainer:
		buf.WriteString(p.ParseDocxBlockQuoteContainer(b))
	case DocxBlockTypeGrid:
		buf.WriteString(p.ParseDocxBlockGrid(b, indentLevel))
	default:
	}
	return buf.String()
}

func (p *Parser) ParseDocxBlockPage(b *larkdocx.Block) string {
	buf := new(strings.Builder)

	buf.WriteString("# ")
	page := b.Page
	if page != nil {
		buf.WriteString(p.ParseDocxBlockText(page))
	}
	buf.WriteString("\n")

	for _, childId := range b.Children {
		childBlock := p.blockMap[childId]
		if childBlock == nil {
			continue
		}
		buf.WriteString(p.ParseDocxBlock(childBlock, 0))
		buf.WriteString("\n")
	}

	return buf.String()
}

func (p *Parser) ParseDocxBlockText(b *larkdocx.Text) string {
	if b == nil {
		return ""
	}
	buf := new(strings.Builder)
	numElem := len(b.Elements)
	for _, e := range b.Elements {
		if e == nil {
			continue
		}
		inline := numElem > 1
		buf.WriteString(p.ParseDocxTextElement(e, inline))
	}
	buf.WriteString("\n")
	return buf.String()
}

func (p *Parser) ParseDocxBlockCallout(b *larkdocx.Block) string {
	buf := new(strings.Builder)

	buf.WriteString(">[!TIP] \n")

	for _, childId := range b.Children {
		childBlock := p.blockMap[childId]
		if childBlock == nil {
			continue
		}
		buf.WriteString(p.ParseDocxBlock(childBlock, 0))
	}

	return buf.String()
}

func (p *Parser) ParseDocxTextElement(e *larkdocx.TextElement, inline bool) string {
	buf := new(strings.Builder)
	if e == nil {
		return ""
	}
	if e.TextRun != nil {
		buf.WriteString(p.ParseDocxTextElementTextRun(e.TextRun))
	}
	if e.MentionUser != nil && e.MentionUser.UserId != nil {
		buf.WriteString(*e.MentionUser.UserId)
	}
	if e.MentionDoc != nil && e.MentionDoc.Title != nil && e.MentionDoc.Url != nil {
		buf.WriteString(
			fmt.Sprintf("[%s](%s)", *e.MentionDoc.Title, utils.UnescapeURL(*e.MentionDoc.Url)))
	}
	if e.Equation != nil && e.Equation.Content != nil {
		symbol := "$$"
		if inline {
			symbol = "$"
		}
		buf.WriteString(symbol + strings.TrimSuffix(*e.Equation.Content, "\n") + symbol)
	}
	return buf.String()
}

func (p *Parser) ParseDocxTextElementTextRun(tr *larkdocx.TextRun) string {
	buf := new(strings.Builder)
	if tr == nil || tr.Content == nil {
		return ""
	}

	// 颜色映射
	backgroundColorMap := map[int]string{
		1:  "#FDEBEC", // 浅红色
		2:  "#FFF4E5", // 浅橙色
		3:  "#FFFBE6", // 浅黄色
		4:  "#E8F8F2", // 浅绿色
		5:  "#EAF6FB", // 浅蓝色
		6:  "#F3F0FA", // 浅紫色
		7:  "#F5F5F5", // 中灰色
		8:  "#F5222D", // 红色
		9:  "#FA8C16", // 橙色
		10: "#FADB14", // 黄色
		11: "#52C41A", // 绿色
		12: "#1890FF", // 蓝色
		13: "#722ED1", // 紫色
		14: "#595959", // 灰色
		15: "#FAFAFA", // 浅灰色
	}
	textColorMap := map[int]string{
		1: "#F5222D", // 红色
		2: "#FA8C16", // 橙色
		3: "#FADB14", // 黄色
		4: "#52C41A", // 绿色
		5: "#1890FF", // 蓝色
		6: "#722ED1", // 紫色
		7: "#595959", // 灰色
	}

	postWrite := ""
	if style := tr.TextElementStyle; style != nil {
		if style.Bold != nil && *style.Bold {
			if p.useHTMLTags {
				buf.WriteString("<strong>")
				postWrite = "</strong>"
			} else {
				buf.WriteString("**")
				postWrite = "**"
			}
		} else if style.Italic != nil && *style.Italic {
			if p.useHTMLTags {
				buf.WriteString("<em>")
				postWrite = "</em>"
			} else {
				buf.WriteString("_")
				postWrite = "_"
			}
		} else if style.Strikethrough != nil && *style.Strikethrough {
			if p.useHTMLTags {
				buf.WriteString("<del>")
				postWrite = "</del>"
			} else {
				buf.WriteString("~~")
				postWrite = "~~"
			}
		} else if style.Underline != nil && *style.Underline {
			buf.WriteString("<u>")
			postWrite = "</u>"
		} else if style.InlineCode != nil && *style.InlineCode {
			buf.WriteString("`")
			postWrite = "`"
		} else if style.Link != nil && style.Link.Url != nil {
			buf.WriteString("[")
			postWrite = fmt.Sprintf("](%s)", utils.UnescapeURL(*style.Link.Url))
		} else if style.BackgroundColor != nil {
			color := backgroundColorMap[*style.BackgroundColor]
			if color != "" {
				buf.WriteString(fmt.Sprintf("<span style=\"background-color: %s;\">", color))
				postWrite = "</span>"
			}
		} else if style.TextColor != nil {
			color := textColorMap[*style.TextColor]
			if color != "" {
				buf.WriteString(fmt.Sprintf("<span style=\"color: %s;\">", color))
				postWrite = "</span>"
			}
		}
	}
	buf.WriteString(*tr.Content)
	buf.WriteString(postWrite)
	return buf.String()
}

func (p *Parser) ParseDocxBlockHeading(b *larkdocx.Block, headingLevel int) string {
	buf := new(strings.Builder)

	buf.WriteString(strings.Repeat("#", headingLevel))
	buf.WriteString(" ")

	v := reflect.ValueOf(b).Elem().FieldByName(fmt.Sprintf("Heading%d", headingLevel))
	if v.IsValid() && !v.IsNil() {
		if text, ok := v.Interface().(*larkdocx.Text); ok && text != nil {
			buf.WriteString(p.ParseDocxBlockText(text))
		}
	}

	for _, childId := range b.Children {
		childBlock := p.blockMap[childId]
		if childBlock == nil {
			continue
		}
		buf.WriteString(p.ParseDocxBlock(childBlock, 0))
	}

	return buf.String()
}

func (p *Parser) ParseDocxBlockImage(img *larkdocx.Image) string {
	buf := new(strings.Builder)
	buf.WriteString(fmt.Sprintf("![](%s)", *img.Token))
	buf.WriteString("\n")
	p.ImgTokens = append(p.ImgTokens, *img.Token)
	return buf.String()
}

func (p *Parser) ParseDocxWhatever(body *lark.DocBody) string {
	buf := new(strings.Builder)

	return buf.String()
}

func (p *Parser) ParseDocxBlockBullet(b *larkdocx.Block, indentLevel int) string {
	buf := new(strings.Builder)

	buf.WriteString("- ")
	buf.WriteString(p.ParseDocxBlockText(b.Bullet))

	for _, childId := range b.Children {
		childBlock := p.blockMap[childId]
		if childBlock == nil {
			continue
		}
		buf.WriteString(p.ParseDocxBlock(childBlock, indentLevel+1))
	}

	return buf.String()
}

func (p *Parser) ParseDocxBlockOrdered(b *larkdocx.Block, indentLevel int) string {
	buf := new(strings.Builder)

	parent := p.blockMap[*b.ParentId]
	if parent == nil {
		return ""
	}
	order := 1
	for idx, child := range parent.Children {
		if child == *b.BlockId {
			for i := idx - 1; i >= 0; i-- {
				if *p.blockMap[parent.Children[i]].BlockType == DocxBlockTypeOrdered {
					order += 1
				} else {
					break
				}
			}
			break
		}
	}

	buf.WriteString(fmt.Sprintf("%d. ", order))
	buf.WriteString(p.ParseDocxBlockText(b.Ordered))

	for _, childId := range b.Children {
		childBlock := p.blockMap[childId]
		if childBlock == nil {
			continue
		}
		buf.WriteString(p.ParseDocxBlock(childBlock, indentLevel+1))
	}

	return buf.String()
}

func (p *Parser) ParseDocxBlockTableCell(b *larkdocx.Block) string {
	buf := new(strings.Builder)

	for _, child := range b.Children {
		block := p.blockMap[child]
		if block == nil {
			continue
		}
		content := p.ParseDocxBlock(block, 0)
		buf.WriteString(content + "<br/>")
	}

	return buf.String()
}

func (p *Parser) ParseDocxBlockTable(t *larkdocx.Table) string {
	var rows [][]string
	mergeInfoMap := map[int]map[int]*larkdocx.TableMergeInfo{}

	if t.Property.MergeInfo != nil {
		for i, merge := range t.Property.MergeInfo {
			rowIndex := int(i) / *t.Property.ColumnSize
			colIndex := int(i) % *t.Property.ColumnSize
			if _, exists := mergeInfoMap[int(rowIndex)]; !exists {
				mergeInfoMap[int(rowIndex)] = map[int]*larkdocx.TableMergeInfo{}
			}
			mergeInfoMap[rowIndex][colIndex] = merge
		}
	}

	for i, blockId := range t.Cells {
		block := p.blockMap[blockId]
		if block == nil {
			continue
		}
		cellContent := p.ParseDocxBlock(block, 0)
		cellContent = strings.ReplaceAll(cellContent, "\n", "")
		rowIndex := int(i) / *t.Property.ColumnSize
		colIndex := int(i) % *t.Property.ColumnSize

		for len(rows) <= int(rowIndex) {
			rows = append(rows, []string{})
		}
		for len(rows[rowIndex]) <= int(colIndex) {
			rows[rowIndex] = append(rows[rowIndex], "")
		}
		rows[rowIndex][colIndex] = cellContent
	}

	buf := new(strings.Builder)
	buf.WriteString("<table>\n")

	processedCells := map[string]bool{}

	for rowIndex, row := range rows {
		buf.WriteString("<tr>\n")
		for colIndex, cellContent := range row {
			cellKey := fmt.Sprintf("%d-%d", rowIndex, colIndex)

			if processedCells[cellKey] {
				continue
			}

			mergeInfo := mergeInfoMap[int(rowIndex)][int(colIndex)]
			if mergeInfo != nil {

				attributes := ""
				if *mergeInfo.RowSpan > 1 {
					attributes += fmt.Sprintf(` rowspan="%d"`, mergeInfo.RowSpan)
				}
				if *mergeInfo.ColSpan > 1 {
					attributes += fmt.Sprintf(` colspan="%d"`, mergeInfo.ColSpan)
				}
				buf.WriteString(fmt.Sprintf(
					`<td%s>%s</td>`,
					attributes, cellContent,
				))
				for r := rowIndex; r < rowIndex+int(*mergeInfo.RowSpan); r++ {
					for c := colIndex; c < colIndex+int(*mergeInfo.ColSpan); c++ {
						processedCells[fmt.Sprintf("%d-%d", r, c)] = true
					}
				}
			} else {
				buf.WriteString(fmt.Sprintf("<td>%s</td>", cellContent))
			}
		}
		buf.WriteString("</tr>\n")
	}
	buf.WriteString("</table>\n")

	return buf.String()
}

func (p *Parser) ParseDocxBlockQuoteContainer(b *larkdocx.Block) string {
	buf := new(strings.Builder)

	for _, child := range b.Children {
		block := p.blockMap[child]
		if block == nil {
			continue
		}
		buf.WriteString("> ")
		buf.WriteString(p.ParseDocxBlock(block, 0))
	}

	return buf.String()
}

func (p *Parser) ParseDocxBlockGrid(b *larkdocx.Block, indentLevel int) string {
	buf := new(strings.Builder)

	for _, child := range b.Children {
		columnBlock := p.blockMap[child]
		if columnBlock == nil {
			continue
		}
		for _, child := range columnBlock.Children {
			block := p.blockMap[child]
			if block == nil {
				continue
			}
			buf.WriteString(p.ParseDocxBlock(block, indentLevel))
		}
	}

	return buf.String()
}

func FetchLarkApi(ctx context.Context, method string, url string, res ...io.Reader) ([]byte, error) {
	var body io.Reader
	if len(res) > 0 {
		body = res[0]
	} else {
		body = nil
	}

	ak, err := base_utils.RefreshAccessToken()

	if err != nil {
		return []byte{}, fmt.Errorf("获取 access_token 失败: %v", err)
	}

	req, err := http.NewRequest(method, url, body)

	req.Header.Set("Authorization", "Bearer "+ak)
	req.Header.Set("Content-Type", "application/json")

	if err != nil {
		return []byte{}, fmt.Errorf("创建请求失败: %v", err)
	}

	client := &http.Client{}
	resp, err := client.Do(req)

	if err != nil {
		return []byte{}, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)

	if err != nil {
		return []byte{}, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return []byte{}, fmt.Errorf("请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
	}

	return respBody, nil
}

func CreateLarkClient(ctx context.Context) *larkSdk.Client {
	client := larkSdk.NewClient(conf.GetAppConfig().AppID, conf.GetAppConfig().AppSecret)
	return client
}
