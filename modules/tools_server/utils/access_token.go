package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strconv"
	"time"

	"code.byted.org/stone/caribou/modules/tools_server/conf"
)

type TokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

func HandleGetAccessToken(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "只支持 GET 请求", http.StatusMethodNotAllowed)
		return
	}

	code := r.URL.Query().Get("code")
	if code == "" {
		http.Error(w, "缺少 code 参数", http.StatusBadRequest)
		return
	}

	ak, rk, err := GetUserAccessToken(code)
	if err != nil {
		http.Error(w, "获取用户 access token 失败", http.StatusInternalServerError)
		return
	}

	err = SaveRefreshToken(ak, rk)
	if err != nil {
		http.Error(w, "保存 refresh token 失败", http.StatusInternalServerError)
		return
	}

	// // TODO: 这里可以添加实际的 token 生成逻辑
	response := TokenResponse{
		AccessToken:  ak,
		RefreshToken: rk,
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		http.Error(w, "编码响应失败: "+err.Error(), http.StatusInternalServerError)
		return
	}
}

func GetUserAccessToken(code string) (string, string, error) {
	// 构建请求体
	requestBody := map[string]string{
		"code":          code,
		"grant_type":    "authorization_code",
		"client_id":     "cli_a24e426730fc500c",
		"client_secret": "hiSc2FOk13Svr27bDNiN4fGuWwXnEnYI",
		"redirect_uri":  "http://localhost:8888/get_access_token",
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return "", "", fmt.Errorf("序列化请求体失败: %v", err)
	}

	// 创建 POST 请求
	req, err := http.NewRequest("POST", "https://open.larkoffice.com/open-apis/authen/v2/oauth/token", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", "", fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	// 如果需要认证头，可以在这里添加
	// req.Header.Set("Authorization", "Bearer your-token")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", "", fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return "", "", fmt.Errorf("请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var response struct {
		AccessToken  string `json:"access_token"`
		RefreshToken string `json:"refresh_token"`
		// 如果需要其他字段，可以在这里添加
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return "", "", fmt.Errorf("解析响应失败: %v", err)
	}

	return response.AccessToken, response.RefreshToken, nil
}

func SaveRefreshToken(accessToken string, refreshToken string) error {
	content := []byte(fmt.Sprintf(`{"accessToken": "%s", "refreshToken": "%s", "expireTime": "%s"}`, accessToken, refreshToken, strconv.FormatInt(time.Now().Add(time.Hour*2).Unix(), 10)))

	err := os.WriteFile("./db.json", content, 0644)
	if err != nil {
		return fmt.Errorf("写入数据失败: %v", err)
	}

	return nil
}

func RefreshAccessToken() (string, error) {
	content, err := os.ReadFile("./db.json")
	if err != nil {
		return "", fmt.Errorf("读取数据失败: %v", err)
	}
	var data map[string]string
	err = json.Unmarshal(content, &data)
	if err != nil {
		return "", fmt.Errorf("解析数据失败: %v", err)
	}

	rk := data["refreshToken"]
	ak := data["accessToken"]
	expireTime, err := strconv.ParseInt(data["expireTime"], 10, 64)
	if err != nil {
		return "", fmt.Errorf("解析过期时间失败: %v", err)
	}

	if time.Now().Unix() < expireTime {
		return ak, nil
	}

	requestBody := map[string]string{
		"grant_type":    "refresh_token",
		"client_id":     conf.GetAppConfig().AppID,
		"client_secret": conf.GetAppConfig().AppSecret,
		"refresh_token": rk,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return "", fmt.Errorf("序列化请求体失败: %v", err)
	}

	// 创建 POST 请求
	req, err := http.NewRequest("POST", "https://open.larkoffice.com/open-apis/authen/v2/oauth/token", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		err := OpenURL("https://accounts.feishu.cn/open-apis/authen/v1/authorize?client_id=cli_a24e426730fc500c&redirect_uri=http://localhost:8888/get_access_token&scope=auth:user.id:read%20docs:document:export%20docs:permission.member:create%20drive:file:download%20drive:file:upload%20search:docs:read%20sheets:spreadsheet.meta:read%20sheets:spreadsheet.meta:write_only%20sheets:spreadsheet:create%20sheets:spreadsheet:read%20space:folder:create%20wiki:wiki:readonly%20offline_access%20search:suite_dataset:readonly%20docx:document:readonly%20docs:document.media:upload%20docs:document:import%20docx:document:create%20docx:document:write_only%20docs:permission.member:transfer%20contact:contact.base:readonly%20drive:drive.metadata:readonly%20board:whiteboard:node:read%20docs:document.media:download")
		return "", fmt.Errorf("请求失败，状态码: %d, 响应: %s, 打开浏览器: %t", resp.StatusCode, string(body), err)
	}

	// 解析响应
	var response struct {
		AccessToken  string `json:"access_token"`
		RefreshToken string `json:"refresh_token"`
		// 如果需要其他字段，可以在这里添加
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return "", fmt.Errorf("解析响应失败: %v", err)
	}

	if err := SaveRefreshToken(response.AccessToken, response.RefreshToken); err != nil {
		return "", fmt.Errorf("保存刷新令牌失败: %v", err)
	}

	return response.AccessToken, nil
}
