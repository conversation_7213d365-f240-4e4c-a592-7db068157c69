package agent_context

import (
	"context"

	"github.com/cloudwego/eino/components/model"
	"github.com/cloudwego/eino/schema"
	"github.com/pkg/errors"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/stone/caribou/pkg/git"
)

func NewContextManager(ctx context.Context, params *NewContextManagerParams) (ContextManager, error) {
	err := params.TaskInput.Repo.CheckoutBranch(params.TaskInput.RepoBaseBranch)
	if err != nil {
		return nil, errors.Wrapf(err, "method=FileSystem.CheckoutBranch failed, branch=%s", params.TaskInput.RepoBaseBranch)
	}

	return &contextManager{
		TaskInput:   params.TaskInput,
		ChatModel:   params.ChatModel,
		repository:  params.TaskInput.Repo,
		taskContext: &TaskContext{},
	}, nil
}

func GetContextManager(ctx context.Context) (ContextManager, error) {
	if manager, ok := ctx.Value(contextManagerKey{}).(ContextManager); ok {
		return manager, nil
	}

	return nil, errors.New("context manager not found")
}

func SetContextManager(ctx context.Context, contextManager ContextManager) context.Context {
	return context.WithValue(ctx, contextManagerKey{}, contextManager)
}

func GetAgentContext(ctx context.Context) (Context, error) {
	if context, ok := ctx.Value(agentContextKey{}).(Context); ok {
		return context, nil
	}

	return nil, errors.New("agent context not found")
}

func SetAgentContext(ctx context.Context, agentContext Context) context.Context {
	return context.WithValue(ctx, agentContextKey{}, agentContext)
}

type contextManager struct {
	SessionID          int64
	TaskInput          *TaskInput
	ChatModel          model.ToolCallingChatModel
	PersistentMessages []*schema.Message

	repository  git.FileSystem
	taskContext *TaskContext
}

func (c *contextManager) GetSessionID() int64 {
	return c.SessionID
}

func (c *contextManager) SaveLongTermMemory(ctx context.Context, agentContext Context) error {
	transientMessages, err := agentContext.GetTransientMessages(ctx)
	if err != nil {
		return errors.Wrapf(err, "method=agentContext.GetPersistentMessages failed")
	}
	c.PersistentMessages = append(c.PersistentMessages, transientMessages...)
	c.taskContext.WorkingBranchName = conv.StringPtr(agentContext.GetRepositoryBranch())
	return nil
}

func (c *contextManager) SetTaskContext(ctx context.Context, taskContext *TaskContext) {
	if taskContext == nil {
		return
	}

	if taskContext.BaseBranchName != nil {
		c.taskContext.BaseBranchName = taskContext.BaseBranchName
	}
	if taskContext.WorkingBranchName != nil {
		c.taskContext.WorkingBranchName = taskContext.WorkingBranchName
	}
}

func (c *contextManager) ForkAgentContext(ctx context.Context, agentName string) Context {
	return &agentContext{
		agentName:         agentName,
		contextManager:    c,
		TransientMessages: []*schema.Message{},
	}
}

type agentContext struct {
	agentName string
	*contextManager
	TransientMessages []*schema.Message
}

func (a *agentContext) GetTaskInput() *TaskInput {
	return a.TaskInput
}

func (a *agentContext) GetChatModel() model.ToolCallingChatModel {
	return a.ChatModel
}

func (a *agentContext) GetRepository() git.FileSystem {
	return a.repository
}

func (a *agentContext) GetRepositoryBranch() string {
	return conv.StringPtrToVal(a.taskContext.WorkingBranchName, "")
}

func (a *agentContext) GetRepositoryBaseBranch() string {
	return conv.StringPtrToVal(a.taskContext.BaseBranchName, "")
}

func (a *agentContext) SetRepositoryBranch(branch string) {
	a.taskContext.WorkingBranchName = conv.StringPtr(branch)
}

func (a *agentContext) GetPersistentMessages(ctx context.Context) ([]*schema.Message, error) {
	return a.PersistentMessages, nil
}

func (a *agentContext) GetTransientMessages(ctx context.Context) ([]*schema.Message, error) {
	return a.TransientMessages, nil
}

func (a *agentContext) SaveTransientMessages(ctx context.Context, messages []*schema.Message) error {
	a.TransientMessages = append(a.TransientMessages, messages...)
	return nil
}
