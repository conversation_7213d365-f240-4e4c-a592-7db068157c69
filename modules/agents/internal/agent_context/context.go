package agent_context

import (
	"context"

	"code.byted.org/stone/caribou/pkg/git"
	"github.com/cloudwego/eino/components/model"
	"github.com/cloudwego/eino/schema"
)

type ContextManager interface {
	GetSessionID() int64

	SaveLongTermMemory(ctx context.Context, agentContext Context) error

	SetTaskContext(ctx context.Context, taskContext *TaskContext)

	ForkAgentContext(ctx context.Context, agentName string) Context
}

type Context interface {
	GetSessionID() int64

	GetTaskInput() *TaskInput

	GetChatModel() model.ToolCallingChatModel

	GetRepository() git.FileSystem

	GetRepositoryBaseBranch() string

	GetRepositoryBranch() string

	SetRepositoryBranch(branch string)

	GetPersistentMessages(ctx context.Context) ([]*schema.Message, error)

	GetTransientMessages(ctx context.Context) ([]*schema.Message, error)

	SaveTransientMessages(ctx context.Context, messages []*schema.Message) error

	// GetKnowledge?
	// SetKnowledge(ctx context.Context, knowledge Knowledge, ...)
}

type contextManagerKey struct{}

type agentContextKey struct{}

type NewContextManagerParams struct {
	TaskInput *TaskInput
	ChatModel model.ToolCallingChatModel
}

type TaskInput struct {
	UserQuery      string
	Repo           git.FileSystem
	RepoBaseBranch string
}

type TaskContext struct {
	BaseBranchName    *string
	WorkingBranchName *string
}
