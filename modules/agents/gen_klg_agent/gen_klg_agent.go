package gen_klg_agent

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"time"

	"code.byted.org/gopkg/lang/conv"
	"github.com/cloudwego/eino-ext/components/model/openai"
	"github.com/cloudwego/eino/callbacks"
	"github.com/cloudwego/eino/compose"
	"github.com/cloudwego/eino/flow/agent"
	"github.com/cloudwego/eino/flow/agent/react"
	"github.com/cloudwego/eino/schema"
	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"

	"code.byted.org/gopkg/logs"
	"code.byted.org/stone/caribou/modules/agents/conf"
	remote_tool "code.byted.org/stone/caribou/modules/agents/tools/remote_tools"
)

func RunGenKlgAgent(ctx context.Context, query string) (content string, err error) {
	conf.MustInit()
	// config := conf.LLMConfig.ModelProvider.Aws37.GetOpenaiModelConfig()
	config := conf.LLMConf.ModelProvider.GPT4o
	cm, err := openai.NewChatModel(ctx, &openai.ChatModelConfig{
		BaseURL:     config.BaseURL,
		APIKey:      config.AK,
		Model:       config.ModelName,
		ByAzure:     config.IsAzure,
		Temperature: conv.Float32Ptr(config.Temperature),
		MaxTokens:   conv.IntPtr(config.MaxToken),
		TopP:        conv.Float32Ptr(config.TopP),
	})
	if err != nil {
		return
	}
	// arkModel, err := ollama.NewChatModel(ctx, &ollama.ChatModelConfig{
	// 	BaseURL: "http://localhost:11434", // Ollama 服务地址
	// 	Model:   "qwen3:14b",              // 模型名称
	// })
	// if err != nil {
	// 	return fmt.Errorf("failed to create chat model: %v", err)
	// }

	// prepare tools
	mcpCli, err := GetStdioMcpCli(ctx)
	if err != nil {
		fmt.Printf("get stdio mcp client failed: %v\n", err)
	}
	defer mcpCli.Close()

	// tools, err := mmcp.GetTools(ctx, &mmcp.Config{
	// 	Cli: mcpCli,
	// 	// ToolNameList: []string{"git_status"},
	// })

	tools, err := remote_tool.GetTools(ctx)

	if err != nil || len(tools) == 0 {
		return "", fmt.Errorf("get tools failed: %v, len(tools):%v", err, len(tools))
	}
	// Print current working directory

	// prepare persona (system prompt) (optional)
	persona := `# Character:
你是一个帮助用户收集信息和撰写知识文档的助手。
工作流程：
1. 根据用户输入的主题，调用企业内部搜索工具(包括文档搜索和代码搜索)，找到相关文档或者代码，并使用浏览器或者飞书文档阅读工具来获得代码或者文档，按照文档的模版撰写知识。
2. 某些技术方案可能有多重实现，请选择最好的一个，并保证生成的文档围绕这个方案来撰写，避免方案中同时出现两个实现。
3. 尽量找到golang, python, javascript, java, c++等语言的代码示例。

工具提示：
1. 你可以使用工具搜索文档和代码，使用工具来访问链接。
2. 当你读取文档时，如果发现文档中有其他链接，你可以判断该链接是否有助于解决用户问题，如果值得访问，请使用工具访问该链接。
3. 当遇到https://bytedance.larkoffice.com链接时，请使用飞书文档读取工具来阅读文档。

撰写知识：
当你收集到足够资料时，请输出你的方案（功能点列表、架构图、修改点列表、测试方案），并询问用户是否需要修改。
文档的模版如下：
1. 文档标题，使用markdown的标题格式，如：# 标题
2. 文档内容，使用markdown的格式，如：## 子标题，围绕核心概念、安装与配置、代码示例与 API 使用、调试与常见问题、安全与性能优化等章节撰写文档内容。
核心概念
- 是什么：库/平台的核心用途和适用场景（例如 Amazon S3 是对象存储服务，适合存图片、视频等非结构化数据）。
- 关键特性：例如版本控制、生命周期管理（S3）、跨区域复制等。
- 基本术语：如 Bucket（S3）、Endpoint、ACL、IAM Policy 等。
- 官方地址：用作用户延伸阅读以了解更多。
安装与配置
- 安装方法：库的安装命令（如 pip install boto3）；平台接入的准备工作（如 AWS 账号、IAM 密钥、SDK 配置）。
- 环境依赖：Python 版本、操作系统兼容性等。
- 权限配置：例如 IAM Role、最小权限原则、临时凭证（STS）。
代码示例与 API 使用
- 常用操作示例：上传/下载文件（如 s3.put_object()）；列出存储桶内容、删除对象等。
- SDK/API 文档链接：直接指向官方文档（如 Boto3 S3 文档）。
- 最佳实践：如分块上传（Multipart Upload）、预签名 URL（Pre-signed URL）生成。
调试与常见问题
- 常见错误：权限不足（AccessDenied）、超时、网络问题；库的版本冲突或弃用警告。
- 日志与监控：如何启用 AWS CloudTrail 或 SDK 的调试日志。
- 排查工具：如 AWS CLI 命令测试（aws s3 ls）、本地 Mock 测试（如 LocalStack）。
安全与性能优化
- 安全建议：加密（SSE-S3/KMS）、禁止公开读写（Block Public Access）；使用临时凭证而非长期密钥。
- 性能优化：多线程上传、CDN 集成（如 CloudFront）、传输加速（S3 Transfer Acceleration）。
你在编写内容的过程中，应当标记出内容的来源，使用markdown格式标记，如：
1. [来自代码搜索,query=xxx](https://code.byted.org/stone/caribou/blob/da5c4af157bee27fb387c0f230e1846488713129/modules/agents/coding_agent/model.go?ref_type=commits#L28:1-34:2)。
2. [来自文档搜索,query=xxx](https://bytedance.larkoffice.com/docx/WzJMdJRwLoeS2vxd236cYvqQnow)。

当你最后输出时，只需要完整的输出markdown格式的知识，不要在开头和末尾输出其他非知识的内容。以` + "`# 标题`" + `开头

`

	// replace tool call checker with a custom one: check all trunks until you get a tool call
	// because some models(claude or doubao 1.5-pro 32k) do not return tool call in the first response
	// uncomment the following code to enable it
	toolCallChecker := func(ctx context.Context, sr *schema.StreamReader[*schema.Message]) (bool, error) {
		defer sr.Close()
		for {
			msg, err := sr.Recv()
			if err != nil {
				if errors.Is(err, io.EOF) {
					// finish
					break
				}

				return false, err
			}

			if len(msg.ToolCalls) > 0 {
				return true, nil
			}
		}
		return false, nil
	}
	ragent, err := react.NewAgent(ctx, &react.AgentConfig{
		Model:                 cm,
		ToolsConfig:           compose.ToolsNodeConfig{Tools: tools},
		StreamToolCallChecker: toolCallChecker, // uncomment it to replace the default tool call checker with custom one
		MaxStep:               50,
	})
	if err != nil {
		return "", fmt.Errorf("failed to create agent: %v", err)
	}

	// if you want ping/pong, use Generate
	// msg, err := agent.Generate(ctx, []*schema.Message{
	// 	{
	// 		Role:    schema.User,
	// 		Content: "我在北京，给我推荐一些菜，需要有口味辣一点的菜，至少推荐有 2 家餐厅",
	// 	},
	// }, react.WithCallbacks(&myCallback{}))
	// if err != nil {
	// 	log.Printf("failed to generate: %v\n", err)
	// 	return
	// }
	// fmt.Println(msg.String())

	sr, err := ragent.Stream(ctx, []*schema.Message{
		{
			Role:    schema.System,
			Content: persona,
		},
		{
			Role:    schema.User,
			Content: query,
		},
	}, agent.WithComposeOptions(compose.WithCallbacks(&AgentLoggerCallback{})))
	if err != nil {
		return "", fmt.Errorf("failed to stream: %v", err)
	}

	defer sr.Close() // remember to close the stream

	logs.Infof("\n\n===== start streaming =====\n\n")

	for {
		msg, err := sr.Recv()
		if err != nil {
			if errors.Is(err, io.EOF) {
				// finish
				break
			}
			// error
			return "", fmt.Errorf("failed to recv: %v", err)
		}

		// 打字机打印
		logs.Infof("--| %v", msg.Content)
		content += msg.Content
	}

	logs.Infof("\n\n===== finished =====\n")
	return content, nil
}

type AgentLoggerCallback struct {
	callbacks.HandlerBuilder // 可以用 callbacks.HandlerBuilder 来辅助实现 callback
}

func (cb *AgentLoggerCallback) OnStart(ctx context.Context, info *callbacks.RunInfo, input callbacks.CallbackInput) context.Context {
	fmt.Println("=========[OnStart]=========")
	inputStr, _ := json.MarshalIndent(input, "", "  ") // nolint: byted_s_returned_err_check
	fmt.Printf("%s\n", string(inputStr))
	return ctx
}

func (cb *AgentLoggerCallback) OnEnd(ctx context.Context, info *callbacks.RunInfo, output callbacks.CallbackOutput) context.Context {
	fmt.Println("=========[OnEnd]=========")
	outputStr, _ := json.MarshalIndent(output, "", "  ") // nolint: byted_s_returned_err_check
	fmt.Println(string(outputStr))
	return ctx
}

func (cb *AgentLoggerCallback) OnError(ctx context.Context, info *callbacks.RunInfo, err error) context.Context {
	fmt.Println("=========[OnError]=========")
	fmt.Println(err)
	return ctx
}

func (cb *AgentLoggerCallback) OnEndWithStreamOutput(ctx context.Context, info *callbacks.RunInfo,
	output *schema.StreamReader[callbacks.CallbackOutput]) context.Context {

	var graphInfoName = react.GraphName

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("[OnEndStream] panic err:", err)
			}
		}()

		defer output.Close() // remember to close the stream in defer

		fmt.Println("=========[OnEndStream]=========")
		for {
			frame, err := output.Recv()
			if errors.Is(err, io.EOF) {
				// finish
				break
			}
			if err != nil {
				fmt.Printf("internal error: %s\n", err)
				return
			}

			s, err := json.Marshal(frame)
			if err != nil {
				fmt.Printf("internal error: %s\n", err)
				return
			}

			if info.Name == graphInfoName { // 仅打印 graph 的输出, 否则每个 stream 节点的输出都会打印一遍
				fmt.Printf("%s: %s\n", info.Name, string(s))
			}
		}

	}()
	return ctx
}

func (cb *AgentLoggerCallback) OnStartWithStreamInput(ctx context.Context, info *callbacks.RunInfo,
	input *schema.StreamReader[callbacks.CallbackInput]) context.Context {
	defer input.Close()
	return ctx
}

func GetStdioMcpCli(ctx context.Context) (*client.Client, error) {
	c, err := client.NewStdioMCPClient(
		"npx",
		[]string{
			"LARK_APP_ID=********************",
			"LARK_APP_SECRET=hiSc2FOk13Svr27bDNiN4fGuWwXnEnYI",
			"AUTH_CALLBACK_PORT=6688",
			"AUTH_CALLBACK_ENDPOINT=/lark/callback",
		},
		"-y",
		"--registry",
		"http://bnpm.byted.org/",
		"@byted/mcp-lark-docs@latest",
	)
	if err != nil {
		logs.Errorf("Failed to create client: %v", err)
		return nil, err
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	initRequest := mcp.InitializeRequest{}
	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	initRequest.Params.ClientInfo = mcp.Implementation{
		Name:    "example-client",
		Version: "1.0.0",
	}

	_, err = c.Initialize(ctx, initRequest)
	if err != nil {
		logs.Errorf("Failed to initialize: %v", err)
		return nil, err
	}
	return c, err
}
