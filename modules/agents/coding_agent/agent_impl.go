package coding_agent

import (
	"context"
	"io"
	"time"

	"github.com/bytedance/sonic"
	"github.com/cloudwego/eino/callbacks"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/compose"
	"github.com/cloudwego/eino/flow/agent"
	"github.com/cloudwego/eino/flow/agent/react"
	"github.com/cloudwego/eino/schema"
	"github.com/pkg/errors"

	"code.byted.org/stone/caribou/modules/agents/internal/agent_context"
	"code.byted.org/stone/caribou/modules/agents/tools"
	"code.byted.org/stone/caribou/pkg/fornax"
	"code.byted.org/stone/caribou/pkg/git"
	"code.byted.org/stone/caribou/utils/file_util"
)

func NewCodingAgent() CodingAgent {
	return &agentImpl{}
}

type agentImpl struct {
}

func (c *agentImpl) Invoke(ctx context.Context, params *InvokeParams, opts ...agent.AgentOption) (*InvokeResult, error) {
	agentContext, err := agent_context.GetAgentContext(ctx)
	if err != nil {
		return nil, errors.Wrapf(err, "method=agent_context.GetAgentContext failed")
	}
	projectStructure, err := file_util.BuildDirectoryTree(agentContext.GetRepository(), nil)
	if err != nil {
		return nil, errors.Wrapf(err, "method=BuildDirectoryTree failed")
	}

	reactAgent, err := react.NewAgent(ctx, &react.AgentConfig{
		ToolCallingModel: agentContext.GetChatModel(),
		ToolsConfig: compose.ToolsNodeConfig{
			Tools: []tool.BaseTool{
				tools.NewReadFileTool(agentContext.GetRepository()),
				tools.NewCreateFileTool(agentContext.GetRepository()),
				tools.NewEditFileTool(agentContext.GetRepository()),
				tools.NewGitCheckoutTool(agentContext.GetRepository(), func(branch string) {
					agentContext.SetRepositoryBranch(branch)
				}),
				tools.NewGitPushTool(agentContext.GetRepository()),
			},
		},
		MaxStep:               2 * params.MaxReActTimes,
		StreamToolCallChecker: firstChunkStreamToolCallChecker,
	})
	if err != nil {
		return nil, errors.Wrapf(err, "method=react.NewAgent failed")
	}
	transientMessage, _ := agentContext.GetTransientMessages(ctx)
	persistentMessage, _ := agentContext.GetPersistentMessages(ctx)
	messages, err := fornax.GetClient().GetMessages(ctx, &fornax.GetMessagesParams{
		Key: "caribou.coding.v0",
		Variables: map[string]any{
			"current_datetime":  time.Now().Format("2006-01-02 15:04:05"),
			"language":          "English", // TODO 先写死，后续修改
			"project_structure": projectStructure,
			"branch_name":       agentContext.GetRepositoryBranch(),
			"user_input":        agentContext.GetTaskInput().UserQuery,
			"current_task":      params.UserTask,
			"base_branch_name":  agentContext.GetRepositoryBaseBranch(),
			// 以下是placeholder变量，需要传入消息列表
			"transient_message":      transientMessage,
			"persistent_term_memory": persistentMessage,
		},
		// 其他变量
	})
	if err != nil {
		return nil, errors.Wrapf(err, "method=fornax.Client.GetMessages failed")
	}
	codeDiff := make(map[string]*CodeDiff)
	result, err := reactAgent.Generate(ctx, messages, agent.WithComposeOptions(compose.WithCallbacks(&callbackHandler{
		codeDiff:   codeDiff,
		repository: agentContext.GetRepository(),
	})))
	if err != nil {
		return nil, errors.Wrapf(err, "method=react.Agent.Generate failed")
	}

	return &InvokeResult{
		Output:   result.String(),
		CodeDiff: codeDiff,
	}, nil
}

func firstChunkStreamToolCallChecker(_ context.Context, sr *schema.StreamReader[*schema.Message]) (bool, error) {
	defer sr.Close()
	for {
		msg, err := sr.Recv()
		if err != nil {
			if errors.Is(err, io.EOF) {
				// finish
				break
			}

			return false, err
		}

		if len(msg.ToolCalls) > 0 {
			return true, nil
		}
	}

	return false, nil
}

type callbackHandler struct {
	codeDiff            map[string]*CodeDiff
	currentCodeDiffPath string
	repository          git.FileSystem
}

func (h *callbackHandler) OnStart(ctx context.Context, info *callbacks.RunInfo, input callbacks.CallbackInput) context.Context {
	if info == nil || (info.Name != tools.NameCreateFileTool && info.Name != tools.NameEditFileTool) {
		return ctx
	}
	args, ok := input.(string)
	if !ok {
		return ctx
	}
	if info.Name == tools.NameCreateFileTool {
		params := &tools.CreateFileToolParams{}
		if err := sonic.Unmarshal([]byte(args), params); err != nil {
			return ctx
		}
		h.currentCodeDiffPath = params.Path
		if _, ok := h.codeDiff[params.Path]; ok {
			return ctx
		}
		h.codeDiff[params.Path] = &CodeDiff{}
	}
	if info.Name == tools.NameEditFileTool {
		params := &tools.EditFileToolParams{}
		if err := sonic.Unmarshal([]byte(args), params); err != nil {
			return ctx
		}
		h.currentCodeDiffPath = params.Path
		if _, ok := h.codeDiff[params.Path]; ok {
			// 存在则不覆盖原文件内容，避免多次edit同一个文件，导致文件的原始内容丢失
			return ctx
		}
		originContent, _ := h.repository.ReadFile(params.Path)
		h.codeDiff[params.Path] = &CodeDiff{
			Origin: string(originContent),
		}
	}

	return ctx
}

func (h *callbackHandler) OnEnd(ctx context.Context, info *callbacks.RunInfo, output callbacks.CallbackOutput) context.Context {
	if info == nil || (info.Name != tools.NameCreateFileTool && info.Name != tools.NameEditFileTool) || len(h.currentCodeDiffPath) <= 0 {
		return ctx
	}
	if info.Name == tools.NameCreateFileTool {
		if _, ok := h.codeDiff[h.currentCodeDiffPath]; !ok {
			return ctx
		}
		content, _ := h.repository.ReadFile(h.currentCodeDiffPath)
		h.codeDiff[h.currentCodeDiffPath].Modified = string(content)
	}
	if info.Name == tools.NameEditFileTool {
		if _, ok := h.codeDiff[h.currentCodeDiffPath]; !ok {
			return ctx
		}
		modifyContent, _ := h.repository.ReadFile(h.currentCodeDiffPath)
		h.codeDiff[h.currentCodeDiffPath].Modified = string(modifyContent)
	}

	return ctx
}

func (h *callbackHandler) OnError(ctx context.Context, info *callbacks.RunInfo, err error) context.Context {
	return ctx
}

func (h *callbackHandler) OnStartWithStreamInput(ctx context.Context, info *callbacks.RunInfo, input *schema.StreamReader[callbacks.CallbackInput]) context.Context {
	defer input.Close()
	return ctx
}
func (h *callbackHandler) OnEndWithStreamOutput(ctx context.Context, info *callbacks.RunInfo, output *schema.StreamReader[callbacks.CallbackOutput]) context.Context {
	defer output.Close()
	return ctx
}

func NewCodingSpecialistAgent() tool.InvokableTool {
	return &specialistAgent{}
}

type specialistAgent struct {
}

func (s *specialistAgent) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "coding_specialist_agent",
		Desc: "coding_specialist_agent is a tool that can help you to write code.",
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"user_task": {
				Type:     schema.String,
				Desc:     "The task that the user wants to do.",
				Required: true,
			},
		}),
	}, nil
}

func (s *specialistAgent) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	params := &InvokeParams{}
	if err := sonic.Unmarshal([]byte(argumentsInJSON), params); err != nil {
		return "", errors.Wrapf(err, "method=sonic.Umarshal failed, failed to parse arguments, arguments=%s", argumentsInJSON)
	}
	if len(params.UserTask) <= 0 {
		return "", errors.New("invalid new branch name")
	}
	if params.MaxReActTimes <= 0 {
		params.MaxReActTimes = 100
	}

	contextManager, _ := agent_context.GetContextManager(ctx)
	agentContext := contextManager.ForkAgentContext(ctx, "coding")
	ctx = agent_context.SetAgentContext(ctx, agentContext)
	defer contextManager.SaveLongTermMemory(ctx, agentContext)
	codingAgent := NewCodingAgent()
	result, err := codingAgent.Invoke(ctx, params)
	if err != nil {
		return "", errors.Wrapf(err, "method=codingAgent.Invoke failed")
	}

	return result.Output, nil
}
