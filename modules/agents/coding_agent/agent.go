package coding_agent

import (
	"context"

	"github.com/cloudwego/eino/flow/agent"
)

type CodingAgent interface {
	Invoke(ctx context.Context, param *InvokeParams, opts ...agent.AgentOption) (*InvokeResult, error)
}

type InvokeParams struct {
	UserTask string `json:"user_task" validate:"required"`

	MaxReActTimes int `json:"max_react_times" validate:"required,gt=0"`
}

type InvokeResult struct {
	Output   string
	CodeDiff map[string]*CodeDiff
}

type CodeDiff struct {
	Origin   string
	Modified string
}
