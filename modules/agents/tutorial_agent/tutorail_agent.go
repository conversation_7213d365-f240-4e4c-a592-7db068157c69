package tutorial_agent

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"

	"github.com/cloudwego/eino-ext/components/model/openai"
	"github.com/cloudwego/eino/callbacks"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/compose"
	"github.com/cloudwego/eino/flow/agent"
	"github.com/cloudwego/eino/flow/agent/react"
	"github.com/cloudwego/eino/schema"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/stone/caribou/modules/agents/conf"
	kgTools "code.byted.org/stone/caribou/modules/agents/tools"
	"code.byted.org/stone/caribou/modules/agents/tools/tool_type"
)

func RunTutorialAgent(query string) (content string, err error) {
	ctx := context.Background()
	conf.MustInit()
	config := conf.LLMConf.ModelProvider.Aws37
	cm, err := openai.NewChatModel(ctx, &openai.ChatModelConfig{
		APIKey:      config.AK,
		BaseURL:     config.BaseURL,
		APIVersion:  config.APIVersion,
		Model:       config.ModelName,
		MaxTokens:   conv.IntPtr(config.MaxToken),
		Temperature: conv.Float32Ptr(config.Temperature),
		TopP:        conv.Float32Ptr(config.TopP),
		ByAzure:     config.IsAzure,
	})
	if err != nil {
		return "", err
	}

	fileTool := kgTools.GetTools(ctx)
	tools := []tool.BaseTool{}

	for _, t := range fileTool {
		info, _ := t.Info(ctx)
		if info.Name == string(tool_type.WRITE_FILE) {
			continue
		}
		tools = append(tools, t)
	}

	// replace tool call checker with a custom one: check all trunks until you get a tool call
	// because some models(claude or doubao 1.5-pro 32k) do not return tool call in the first response
	// uncomment the following code to enable it
	toolCallChecker := func(ctx context.Context, sr *schema.StreamReader[*schema.Message]) (bool, error) {
		defer sr.Close()
		for {
			msg, err := sr.Recv()
			if err != nil {
				if errors.Is(err, io.EOF) {
					// finish
					break
				}

				return false, err
			}

			if len(msg.ToolCalls) > 0 {
				return true, nil
			}
		}
		return false, nil
	}
	ragent, err := react.NewAgent(ctx, &react.AgentConfig{
		Model:                 cm,
		ToolsConfig:           compose.ToolsNodeConfig{Tools: tools},
		StreamToolCallChecker: toolCallChecker, // uncomment it to replace the default tool call checker with custom one
		MaxStep:               30,
	})
	if err != nil {
		return "", fmt.Errorf("failed to create agent: %v", err)
	}

	sr, err := ragent.Stream(ctx, []*schema.Message{
		{
			Role:    schema.System,
			Content: genTutorialSystemPrompt,
		},
		{
			Role:    schema.User,
			Content: query,
		},
	}, agent.WithComposeOptions(compose.WithCallbacks(&AgentLoggerCallback{})))
	if err != nil {
		return "", fmt.Errorf("failed to stream: %v", err)
	}

	defer sr.Close() // remember to close the stream

	logs.Infof("\n\n===== start streaming =====\n\n")

	for {
		msg, err := sr.Recv()
		if err != nil {
			if errors.Is(err, io.EOF) {
				// finish
				break
			}
			// error
			return "", fmt.Errorf("failed to recv: %v", err)
		}

		// 打字机打印
		logs.Infof("--| %v", msg.Content)
		content += msg.Content
	}

	logs.Infof("\n\n===== finished =====\n")
	return content, nil
}

type AgentLoggerCallback struct {
	callbacks.HandlerBuilder // 可以用 callbacks.HandlerBuilder 来辅助实现 callback
}

func (cb *AgentLoggerCallback) OnStart(ctx context.Context, info *callbacks.RunInfo, input callbacks.CallbackInput) context.Context {
	fmt.Println("=========[OnStart]=========")
	inputStr, _ := json.MarshalIndent(input, "", "  ") // nolint: byted_s_returned_err_check
	fmt.Printf("%s\n", string(inputStr))
	return ctx
}

func (cb *AgentLoggerCallback) OnEnd(ctx context.Context, info *callbacks.RunInfo, output callbacks.CallbackOutput) context.Context {
	fmt.Println("=========[OnEnd]=========")
	outputStr, _ := json.MarshalIndent(output, "", "  ") // nolint: byted_s_returned_err_check
	fmt.Println(string(outputStr))
	return ctx
}

func (cb *AgentLoggerCallback) OnError(ctx context.Context, info *callbacks.RunInfo, err error) context.Context {
	fmt.Println("=========[OnError]=========")
	fmt.Println(err)
	return ctx
}

func (cb *AgentLoggerCallback) OnEndWithStreamOutput(ctx context.Context, info *callbacks.RunInfo,
	output *schema.StreamReader[callbacks.CallbackOutput]) context.Context {

	var graphInfoName = react.GraphName

	go func() {
		defer func() {
			if err := recover(); err != nil {
				fmt.Println("[OnEndStream] panic err:", err)
			}
		}()

		defer output.Close() // remember to close the stream in defer

		fmt.Println("=========[OnEndStream]=========")
		for {
			frame, err := output.Recv()
			if errors.Is(err, io.EOF) {
				// finish
				break
			}
			if err != nil {
				fmt.Printf("internal error: %s\n", err)
				return
			}

			s, err := json.Marshal(frame)
			if err != nil {
				fmt.Printf("internal error: %s\n", err)
				return
			}

			if info.Name == graphInfoName { // 仅打印 graph 的输出, 否则每个 stream 节点的输出都会打印一遍
				fmt.Printf("%s: %s\n", info.Name, string(s))
			}
		}

	}()
	return ctx
}

func (cb *AgentLoggerCallback) OnStartWithStreamInput(ctx context.Context, info *callbacks.RunInfo,
	input *schema.StreamReader[callbacks.CallbackInput]) context.Context {
	defer input.Close()
	return ctx
}
