package tutorial_agent

//import (
//	"context"
//	"testing"
//
//	"code.byted.org/gopkg/logs"
//)
//
//func TestGenerateRepoTasks(t *testing.T) {
//
//	repoUrl := "https://code.byted.org/gopkg/myredis.git"
//
//	repoPath, err := GenerateRepoTasks(context.Background(), repoUrl)
//	if err != nil {
//		t.Fatalf("prepareRepo error: %v", err)
//	}
//	logs.Infof("repo path:%s\n", repoPath)
//	logs.Flush()
//}
