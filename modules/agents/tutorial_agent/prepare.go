package tutorial_agent

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"code.byted.org/gopkg/logs"
)

const (
	repoDir = "./repoDir/"
)

func runShell(ctx context.Context, cmdStr string) (string, error) {
	// 只允许执行go doc和 git 命令
	if !strings.HasPrefix(cmdStr, "go doc") && !strings.HasPrefix(cmdStr, "git") {
		return "", fmt.Errorf("不允许执行非go doc和git命令: %s", cmdStr)
	}
	cmd := exec.CommandContext(ctx, "/bin/sh", "-c", cmdStr)
	output, err := cmd.CombinedOutput()
	return string(output), err
}

func DownloadRepo(ctx context.Context, repoUrl string) (absPath string, err error) {
	// 获取repoUrl的commit hash
	printCurrentDir(ctx, "download begin")
	commitHash, err := getRepoMasterCommitHash(ctx, repoUrl)
	if err != nil {
		return "", err
	}
	// 检查 repoDir 目录是否存在
	if _, err := os.Stat(repoDir); os.IsNotExist(err) {
		// 目录不存在,创建目录
		if err := os.MkdirAll(repoDir, 0755); err != nil {
			return "", fmt.Errorf("创建目录失败: %v", err)
		}
	}
	// 从 repoUrl 中提取仓库名称作为本地目录
	parts := strings.Split(repoUrl, "/")
	repoName := strings.TrimSuffix(parts[len(parts)-1], ".git")
	localPath := repoName
	targetRepoPath := fmt.Sprintf("%s_%s_DONE", localPath, commitHash)
	fullTargetRepoPath := filepath.Join(repoDir, targetRepoPath)
	// 检查目标路径是否存在
	if _, err := os.Stat(fullTargetRepoPath); err == nil {
		// 目标路径已存在,直接返回
		absPath, err := filepath.Abs(fullTargetRepoPath)
		if err != nil {
			return "", fmt.Errorf("获取绝对路径失败: %v", err)
		}
		logs.Infof("repo %s already downloaded", repoUrl)
		return absPath, nil
	}

	// 进入 repoDir 目录
	if err := os.Chdir(repoDir); err != nil {
		return "", fmt.Errorf("切换目录失败: %v", err)
	}
	printCurrentDir(ctx, "after go to repoDir")
	// 先使用人类可读时间戳拼接出临时文件夹
	timestamp := time.Now().Format("2006-01-02_15-04-05")
	downloadPath := fmt.Sprintf("%s_%s", localPath, timestamp)
	// 执行 git clone
	cloneCmd := fmt.Sprintf("git clone %s %s", repoUrl, downloadPath)
	if output, err := runShell(ctx, cloneCmd); err != nil {
		return "", fmt.Errorf("git clone 失败: %v, 输出: %s", err, output)
	}
	printCurrentDir(ctx, "before rename")
	// 将下载的目录重命名为目标路径
	if err := os.Rename(downloadPath, targetRepoPath); err != nil {
		return "", fmt.Errorf("重命名目录失败: %v", err)
	}

	// 获取目标路径的绝对路径
	absPath, err = filepath.Abs(targetRepoPath)
	if err != nil {
		return "", fmt.Errorf("获取绝对路径失败: %v", err)
	}
	logs.Infof("repo %s downloaded to %s", repoUrl, absPath)
	return absPath, nil
}

func getRepoMasterCommitHash(ctx context.Context, repoUrl string) (hash string, err error) {
	// 1. 获取主干分支名称
	cmdBranch := fmt.Sprintf("git ls-remote --symref %s HEAD | head -1 | awk '{print $2}'", repoUrl)
	branchName, err := runShell(ctx, cmdBranch)
	if err != nil {
		return "", fmt.Errorf("获取主分支名称失败: %v, 输出: %s", err, branchName)
	}
	branchName = strings.TrimSpace(branchName)
	if branchName == "" {
		return "", fmt.Errorf("未获取到主分支名称")
	}

	// 2. 获取主干分支的commit hash
	cmdHash := fmt.Sprintf("git ls-remote %s %s | awk '{print $1}' | cut -c 1-6", repoUrl, branchName)
	hash, err = runShell(ctx, cmdHash)
	if err != nil {
		return "", fmt.Errorf("获取主分支commit hash失败: %v, 输出: %s", err, hash)
	}
	hash = strings.TrimSpace(hash)
	if hash == "" {
		return "", fmt.Errorf("未获取到主分支commit hash")
	}
	return hash, nil
}

func GenerateRepoTasks(ctx context.Context, repoPath string) (taskList []string, err error) {
	// 进入 repoPath 目录
	if err := os.Chdir(repoPath); err != nil {
		return nil, fmt.Errorf("切换目录失败: %v", err)
	}

	// 执行 go doc 命令获取所有公开的符号
	goDocCmd := "go doc -all"
	output, err := runShell(ctx, goDocCmd)
	if err != nil {
		return nil, fmt.Errorf("执行 go doc 命令失败: %v, 输出: %s", err, output)
	}

	return splitGoDocOutput(output), nil
}

func splitGoDocOutput(output string) (segmentList []string) {
	lines := strings.Split(output, "\n")
	var segment strings.Builder
	for _, line := range lines {
		trimmed := strings.TrimLeft(line, " \t")
		// Package/CONSTANTS/TYPES/FUNCTIONS/VARIABLES开头的行
		if strings.HasPrefix(trimmed, "Package") ||
			strings.HasPrefix(trimmed, "package") ||
			strings.HasPrefix(trimmed, "CONSTANTS") ||
			strings.HasPrefix(trimmed, "TYPES") ||
			strings.HasPrefix(trimmed, "FUNCTIONS") ||
			strings.HasPrefix(trimmed, "VARIABLES") {
			continue
		}

		// 检查是否是新segment的开始
		if strings.HasPrefix(line, "type ") ||
			strings.HasPrefix(line, "func ") ||
			strings.HasPrefix(line, "const ") ||
			strings.HasPrefix(line, "var ") {
			if segment.Len() > 0 && strings.TrimSpace(segment.String()) != "" {
				segmentList = append(segmentList, strings.TrimSpace(segment.String()))
				segment.Reset()
			}
		}
		segment.WriteString(line + "\n")
	}
	if segment.Len() > 0 && strings.TrimSpace(segment.String()) != "" {
		segmentList = append(segmentList, strings.TrimSpace(segment.String()))
	}
	return
}

func printCurrentDir(ctx context.Context, tag string) {
	// 打印当前目录
	pwd, err := os.Getwd()
	if err != nil {
		logs.CtxError(ctx, "获取当前目录失败[%s], err:%v", tag, err)
	}
	logs.Infof("当前目录[%s]: %s", tag, pwd)
}
