package workflows

import (
	"context"

	"github.com/cloudwego/eino/compose"
	"github.com/cloudwego/eino/flow/agent"
	"github.com/pkg/errors"

	"code.byted.org/flowdevops/fornax_sdk/infra/ob"
	"code.byted.org/gopkg/logid"
	"code.byted.org/kite/kitutil"
	"code.byted.org/stone/caribou/modules/agents/architect_agent"
	"code.byted.org/stone/caribou/modules/agents/coding_agent"
	"code.byted.org/stone/caribou/modules/agents/conf"
	"code.byted.org/stone/caribou/modules/agents/internal/agent_context"
	"code.byted.org/stone/caribou/modules/agents/internal/prefill_model"
	"code.byted.org/stone/caribou/pkg/fornax"
	"code.byted.org/stone/caribou/pkg/git"
)

const (
	architectMaxReActTimes = 25
	codingMaxReActTimes    = 50
	maxStep                = 100
)

type firstWorkflowState struct {
	tasks            []string
	baseBranchName   string
	currentTaskIndex int
	codeDiff         map[string]*coding_agent.CodeDiff
}

func NewFirstWorkflow(ctx context.Context) (*FirstWorkflow, error) {
	// 定义nodes
	const (
		nodeKeyArchitect        = "architect"
		nodeKeyArchitect2Coding = "architect_to_coding"
		nodeKeyCoding           = "coding"
		nodeKeyCoding2Coding    = "coding_to_coding"
		nodeKeyCoding2Finish    = "coding_to_finish"
	)

	graph := compose.NewGraph[string, *FirstWorkFlowResult](compose.WithGenLocalState(func(ctx context.Context) *firstWorkflowState {
		return &firstWorkflowState{
			codeDiff: make(map[string]*coding_agent.CodeDiff),
		}
	}))
	_ = graph.AddLambdaNode(nodeKeyArchitect, compose.InvokableLambda(func(ctx context.Context, userQuery string) (*architect_agent.InvokeResult, error) {
		contextManager, _ := agent_context.GetContextManager(ctx)
		agentContext := contextManager.ForkAgentContext(ctx, "architect")
		ctx = agent_context.SetAgentContext(ctx, agentContext)
		defer contextManager.SaveLongTermMemory(ctx, agentContext)

		result, err := architect_agent.NewArchitectAgent().Invoke(ctx, &architect_agent.InvokeParams{
			Query:         userQuery,
			MaxReActTimes: architectMaxReActTimes,
		})
		if err != nil {
			return nil, errors.Wrapf(err, "method=ArchitectAgent.Invoke failed")
		}

		state, _ := compose.GetState[*firstWorkflowState](ctx)
		state.tasks = result.Tasks
		return result, nil
	}), compose.WithNodeName(nodeKeyArchitect))
	_ = graph.AddLambdaNode(nodeKeyArchitect2Coding, compose.InvokableLambda(func(ctx context.Context, architectResult *architect_agent.InvokeResult) (*coding_agent.InvokeParams, error) {
		state, _ := compose.GetState[*firstWorkflowState](ctx)
		return &coding_agent.InvokeParams{
			UserTask:      state.tasks[state.currentTaskIndex],
			MaxReActTimes: codingMaxReActTimes,
		}, nil
	}), compose.WithNodeName(nodeKeyArchitect2Coding))
	_ = graph.AddLambdaNode(nodeKeyCoding, compose.InvokableLambda(func(ctx context.Context, params *coding_agent.InvokeParams) (*coding_agent.InvokeResult, error) {
		contextManager, _ := agent_context.GetContextManager(ctx)
		agentContext := contextManager.ForkAgentContext(ctx, "coding")
		ctx = agent_context.SetAgentContext(ctx, agentContext)
		defer contextManager.SaveLongTermMemory(ctx, agentContext)
		result, err := coding_agent.NewCodingAgent().Invoke(ctx, params)
		if err != nil {
			return nil, errors.Wrapf(err, "method=CodingAgent.Invoke failed")
		}
		state, _ := compose.GetState[*firstWorkflowState](ctx)
		state.currentTaskIndex += 1
		for path, diff := range result.CodeDiff {
			if _, ok := state.codeDiff[path]; !ok {
				state.codeDiff[path] = diff
			} else {
				// 存在则不覆盖原文件内容，避免多次edit同一个文件，导致文件的原始内容丢失
				state.codeDiff[path].Modified = diff.Modified
			}
		}

		return result, nil
	}), compose.WithNodeName(nodeKeyCoding))
	_ = graph.AddLambdaNode(nodeKeyCoding2Coding, compose.InvokableLambda(func(ctx context.Context, codingResult *coding_agent.InvokeResult) (*coding_agent.InvokeParams, error) {
		state, _ := compose.GetState[*firstWorkflowState](ctx)
		return &coding_agent.InvokeParams{
			UserTask:      state.tasks[state.currentTaskIndex],
			MaxReActTimes: codingMaxReActTimes,
		}, nil
	}), compose.WithNodeName(nodeKeyCoding2Coding))
	_ = graph.AddLambdaNode(nodeKeyCoding2Finish, compose.InvokableLambda(func(ctx context.Context, codingResult *coding_agent.InvokeResult) (*FirstWorkFlowResult, error) {
		state, _ := compose.GetState[*firstWorkflowState](ctx)
		return &FirstWorkFlowResult{
			CodeDiff: state.codeDiff,
		}, nil
	}), compose.WithNodeName(nodeKeyCoding2Finish))

	_ = graph.AddEdge(compose.START, nodeKeyArchitect)
	_ = graph.AddEdge(nodeKeyArchitect, nodeKeyArchitect2Coding)
	_ = graph.AddEdge(nodeKeyArchitect2Coding, nodeKeyCoding)
	_ = graph.AddEdge(nodeKeyCoding2Coding, nodeKeyCoding)
	_ = graph.AddBranch(nodeKeyCoding, compose.NewGraphBranch(func(ctx context.Context, _ *coding_agent.InvokeResult) (endNode string, err error) {
		state, _ := compose.GetState[*firstWorkflowState](ctx)
		if state.currentTaskIndex >= len(state.tasks) {
			return nodeKeyCoding2Finish, nil
		}
		return nodeKeyCoding2Coding, nil
	}, map[string]bool{nodeKeyCoding2Coding: true, nodeKeyCoding2Finish: true}))
	_ = graph.AddEdge(nodeKeyCoding2Finish, compose.END)
	// 编译 graph，将节点、边、分支转化为面向运行时的结构。由于 graph 中存在环，使用 AnyPredecessor 模式，同时设置运行时最大步数。
	runnable, err := graph.Compile(ctx,
		compose.WithNodeTriggerMode(compose.AnyPredecessor),
		compose.WithMaxRunSteps(maxStep),
		compose.WithGraphName("FirstWorkflow"))
	if err != nil {
		return nil, err
	}

	return &FirstWorkflow{
		runnable: runnable,
	}, nil
}

type FirstWorkflow struct {
	runnable compose.Runnable[string, *FirstWorkFlowResult]
}

func (r *FirstWorkflow) Generate(ctx context.Context, params *FirstWorkFlowParams, opts ...agent.AgentOption) (output *FirstWorkFlowResult, err error) {
	if params == nil || params.Repo == nil || len(params.Input) <= 0 {
		return nil, errors.New("invalid params")
	}

	config := conf.LLMConf.ModelProvider.Aws37
	cm, err := prefill_model.NewOpenaiChatModel(ctx, config)
	if err != nil {
		return nil, errors.Wrapf(err, "method=prefill_model.NewOpenaiChatModel failed")
	}
	contextManager, err := agent_context.NewContextManager(ctx, &agent_context.NewContextManagerParams{
		TaskInput: &agent_context.TaskInput{
			UserQuery:      params.Input,
			Repo:           params.Repo,
			RepoBaseBranch: params.BaseBranchName,
		},
		ChatModel: cm,
	})
	if err != nil {
		return nil, errors.Wrapf(err, "method=agent_context.NewContextManager failed")
	}
	contextManager.SetTaskContext(ctx, &agent_context.TaskContext{
		BaseBranchName: &params.BaseBranchName,
	})
	ctx = agent_context.SetContextManager(ctx, contextManager)
	if _, ok := kitutil.GetCtxLogID(ctx); !ok {
		ctx = kitutil.NewCtxWithLogID(ctx, logid.NewLogID().GenLogID())
	}
	var span ob.FornaxSpan
	span, ctx, err = fornax.GetClient().StartSpan(ctx, "FirstWorkflow", "workflow")
	if err != nil {
		return nil, errors.Wrapf(err, "method=FornaxClient.StartSpan failed")
	}
	span.SetThreadIDBaggage(ctx, logid.NewLogID().GenLogID())
	defer span.Finish(ctx)

	output, err = r.runnable.Invoke(ctx, params.Input, append(agent.GetComposeOptions(opts...), compose.WithCallbacks(fornax.GetClient().NewDefaultCallbackHandler()))...)
	if err != nil {
		return nil, err
	}

	return output, nil
}

type FirstWorkFlowParams struct {
	Input          string
	Repo           git.FileSystem
	BaseBranchName string
}

type FirstWorkFlowResult struct {
	CodeDiff map[string]*coding_agent.CodeDiff
}
