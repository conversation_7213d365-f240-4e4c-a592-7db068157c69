package architect_agent

import (
	"context"
	"encoding/xml"
	"fmt"
	"io"
	"regexp"
	"strings"

	"github.com/bytedance/sonic"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/compose"
	"github.com/cloudwego/eino/flow/agent/react"
	"github.com/cloudwego/eino/schema"
	"github.com/pkg/errors"

	"code.byted.org/stone/caribou/modules/agents/internal/agent_context"
	"code.byted.org/stone/caribou/modules/agents/tools"
	"code.byted.org/stone/caribou/pkg/fornax"
	"code.byted.org/stone/caribou/utils/file_util"
)

var (
	docRegex  = regexp.MustCompile(`<tech_doc>([\s\S]*?)</tech_doc>`)
	taskRegex = regexp.MustCompile(`<sub_task>([\s\S]*?)</sub_task>`)
)

type architectAgentImpl struct {
}

func NewArchitectAgent() ArchitectAgent {
	return &architectAgentImpl{}
}

func (a *architectAgentImpl) Invoke(ctx context.Context, params *InvokeParams) (*InvokeResult, error) {
	agentContext, err := agent_context.GetAgentContext(ctx)
	if err != nil {
		return nil, errors.Wrapf(err, "method=agent_context.GetContextManager failed")
	}
	projectStructure, err := file_util.BuildDirectoryTree(agentContext.GetRepository(), nil)
	if err != nil {
		return nil, errors.Wrapf(err, "method=BuildDirectoryTree failed")
	}

	reactAgent, err := react.NewAgent(ctx, &react.AgentConfig{
		ToolCallingModel: agentContext.GetChatModel(),
		ToolsConfig: compose.ToolsNodeConfig{
			Tools: []tool.BaseTool{
				tools.NewUserInteractionTool(),
				tools.NewReadFileTool(agentContext.GetRepository()),
			},
		},
		MaxStep: 2 * params.MaxReActTimes,
	})
	if err != nil {
		return nil, errors.Wrapf(err, "method=react.NewAgent failed")
	}
	messages, err := fornax.GetClient().GetMessages(ctx, &fornax.GetMessagesParams{
		Key: "caribou.architect.v0",
		Variables: map[string]any{
			"project_structure": projectStructure,
			"user_input":        params.Query,
		},
	})
	if err != nil {
		return nil, errors.Wrapf(err, "method=fornax.Client.GetMessages failed")
	}
	sr, err := reactAgent.Stream(ctx, messages)
	if err != nil {
		return nil, errors.Wrapf(err, "method=ragent.Stream failed")
	}
	defer sr.Close()

	result := &schema.Message{}
	for {
		chunk, err := sr.Recv()
		if err != nil {
			if errors.Is(err, io.EOF) {
				break
			}
			return nil, errors.Wrapf(err, "method=StreamReader.Recv failed")
		}
		result, _ = schema.ConcatMessages([]*schema.Message{result, chunk})
	}
	// 解析result
	invokeResult := &InvokeResult{}
	if doc := docRegex.FindStringSubmatch(result.Content); len(doc) > 0 {
		invokeResult.TechDoc = strings.TrimSpace(doc[1])
	}
	if tasks := taskRegex.FindAllStringSubmatch(result.Content, -1); len(tasks) > 0 {
		for _, task := range tasks {
			invokeResult.Tasks = append(invokeResult.Tasks, strings.TrimSpace(task[1]))
		}
	}

	return invokeResult, nil
}

func NewArchitectSpecialistAgent() tool.InvokableTool {
	return &specialistAgent{}
}

type specialistAgent struct {
}

func (s *specialistAgent) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "architect_specialist_agent",
		Desc: "architect_specialist_agent is a tool can help you to generate architectural design documents and sub-tasks, therefore, you can use it to help you to generate code.",
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"query": {
				Type:     schema.String,
				Desc:     "The query that the user wants to ask.",
				Required: true,
			},
		}),
	}, nil
}

func (s *specialistAgent) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	params := &InvokeParams{}
	if err := sonic.Unmarshal([]byte(argumentsInJSON), params); err != nil {
		return "", errors.Wrapf(err, "method=sonic.Umarshal failed, failed to parse arguments, arguments=%s", argumentsInJSON)
	}
	if len(params.Query) <= 0 {
		return "", errors.New("invalid new branch name")
	}
	if params.MaxReActTimes <= 0 {
		params.MaxReActTimes = 100
	}

	contextManager, _ := agent_context.GetContextManager(ctx)
	agentContext := contextManager.ForkAgentContext(ctx, "coding")
	ctx = agent_context.SetAgentContext(ctx, agentContext)
	defer contextManager.SaveLongTermMemory(ctx, agentContext)
	architectAgent := NewArchitectAgent()
	result, err := architectAgent.Invoke(ctx, params)
	if err != nil {
		return "", errors.Wrapf(err, "method=architectAgent.Invoke failed")
	}
	tasks := struct {
		XMLName xml.Name `xml:"sub_tasks"`
		Tasks   []string `xml:"sub_task"`
	}{
		Tasks: result.Tasks,
	}
	tasksStr, err := xml.MarshalIndent(tasks, "", "  ")
	if err != nil {
		return "", errors.Wrapf(err, "method=xml.MarshalIndent failed")
	}

	return fmt.Sprintf("<architectual_design>\n%s\n</architectual_design>\n%s", result.TechDoc, tasksStr), nil
}
