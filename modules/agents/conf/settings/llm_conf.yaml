model_provider:
  aws_sdk_claude37_sonnet:
    model_policy: openai
    ak: "1xEW2MZUFRkLGnOdIl869DmdlRZZZHkF_GPT_AK"
    base_url: "https://gpt-i18n.byteintl.net/gpt/openapi/online/v2/crawl"
    api_version: ""
    model_name: "aws_sdk_claude37_sonnet"
    max_token: 8192
    temperature: 0.7
    top_p: 0.95
    is_azure: true

  gcp-claude37-sonnet:
    model_policy: openai
    ak: "1xEW2MZUFRkLGnOdIl869DmdlRZZZHkF_GPT_AK"
    base_url: "https://gpt-i18n.byteintl.net/gpt/openapi/online/v2/crawl"
    api_version: ""
    model_name: "gcp-claude37-sonnet"
    max_token: 8192
    temperature: 0.7
    top_p: 0.95
    is_azure: true

  gcp-claude4-sonnet:
    model_policy: openai
    ak: "1xEW2MZUFRkLGnOdIl869DmdlRZZZHkF_GPT_AK"
    base_url: "https://gpt-i18n.byteintl.net/gpt/openapi/online/v2/crawl"
    api_version: ""
    model_name: "gcp-claude4-sonnet"
    max_token: 8192
    temperature: 0.7
    top_p: 0.95
    is_azure: true

  gpt-4o-mini-2024-07-18:
    model_policy: openai
    ak: "1xEW2MZUFRkLGnOdIl869DmdlRZZZHkF_GPT_AK"
    base_url: "https://gpt-i18n.byteintl.net/gpt/openapi/online/v2/crawl"
    api_version: ""
    model_name: "gpt-4o-mini-2024-07-18"
    max_token: 4096
    temperature: 0.7
    top_p: 0.95
    is_azure: true

