package conf

import (
	"embed"

	"gopkg.in/yaml.v3"

	"code.byted.org/gopkg/env"
)

//go:embed settings/*
var staticFiles embed.FS

var LLMConf *llmConfig

func MustInit() {
	mustInitLLMConfig()
	mustInitAppConfig()
	mustInitFornaxConfig()
	mustInitCodeBaseConfig()
	mustInitTosConfig()
}

func mustInitLLMConfig() {
	data, err := staticFiles.ReadFile("settings/llm_conf.yaml")
	if err != nil {
		panic(err)
	}
	LLMConf = &llmConfig{}
	err = yaml.Unmarshal(data, LLMConf)
	if err != nil {
		panic(err)
	}
}

func mustInitAppConfig() {
	data, err := staticFiles.ReadFile("settings/app_conf.yaml")
	if err != nil {
		panic(err)
	}
	err = yaml.Unmarshal(data, &appConfig)
	if err != nil {
		panic(err)
	}
}

func mustInitFornaxConfig() {
	data, err := staticFiles.ReadFile("settings/fornax_conf.yaml")
	if err != nil {
		panic(err)
	}
	fornaxConfig = &fornaxConf{}
	err = yaml.Unmarshal(data, &fornaxConfig)
	if err != nil {
		panic(err)
	}
}

func mustInitCodeBaseConfig() {
	data, err := staticFiles.ReadFile("settings/code_base_conf.yaml")
	if err != nil {
		panic(err)
	}
	codeBaseConfig = &codeBaseConf{}
	err = yaml.Unmarshal(data, &codeBaseConfig)
	if err != nil {
		panic(err)
	}
}

func mustInitTosConfig() {
	data, err := staticFiles.ReadFile("settings/tos_conf.yaml")
	if err != nil {
		panic(err)
	}
	tosConfig = &tosConf{}
	err = yaml.Unmarshal(data, &tosConfig)
	if err != nil {
		panic(err)
	}
}

func isBoe() bool {
	return env.IsBoe()
}

func isBoeCN() bool {
	return env.IsBoeCN()
}

func isBoeI18N() bool {
	return env.IsBoeI18N()
}

func isCN() bool {
	return env.Region() == env.R_CN
}

func isPPE() bool {
	return env.IsPPE()
}
