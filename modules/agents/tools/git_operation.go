package tools

import (
	"context"
	"fmt"

	"github.com/bytedance/sonic"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
	"github.com/pkg/errors"

	"code.byted.org/stone/caribou/pkg/git"
)

var _ tool.InvokableTool = &GitCheckoutTool{}
var _ tool.InvokableTool = &GitPushTool{}

func NewGitCheckoutTool(repository git.FileSystem, invokePostFunc func(checkoutBranch string)) *GitCheckoutTool {
	return &GitCheckoutTool{
		repository:     repository,
		invokePostFunc: invokePostFunc,
	}
}

type GitCheckoutTool struct {
	repository     git.FileSystem
	invokePostFunc func(checkoutBranch string)
}

func (t *GitCheckoutTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "git_checkout_tool",
		Desc: "git checkout工具，可以用此创建新的分支、或切换到某个已有的分支",
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"create_new_branch": {
				Type:     schema.Boolean,
				Desc:     "是否需要创建新的分支",
				Required: true,
			},
			"base_branch_name": {
				Type:     schema.String,
				Desc:     "基于哪个分支创建新分支",
				Required: true,
			},
			"new_branch_name": {
				Type:     schema.String,
				Desc:     "新分支的名字",
				Required: true,
			},
		}),
	}, nil
}

func (t *GitCheckoutTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	var params struct {
		CreateNewBranch bool   `json:"create_new_branch"`
		BaseBranchName  string `json:"base_branch_name"`
		NewBranchName   string `json:"new_branch_name"`
	}
	if err := sonic.Unmarshal([]byte(argumentsInJSON), &params); err != nil {
		return "", errors.Wrapf(err, "method=sonic.Umarshal failed, failed to parse arguments, arguments=%s", argumentsInJSON)
	}
	if len(params.NewBranchName) <= 0 {
		return "", errors.New("invalid new branch name")
	}
	if params.CreateNewBranch {
		exist, err := t.repository.CheckoutNewBranch(params.BaseBranchName, params.NewBranchName)
		if err != nil {
			return "", errors.Wrapf(err, "method=git.FileSystem.CheckoutNewBranch failed")
		}
		if exist {
			return fmt.Sprintf("the branch name %s is already in use, please generate a new branch name for the new branch.", params.NewBranchName), nil
		}
	} else {
		err := t.repository.CheckoutBranch(params.NewBranchName)
		if err != nil {
			return "", errors.Wrapf(err, "method=git.FileSystem.CheckoutBranch failed")
		}
	}
	if t.invokePostFunc != nil {
		t.invokePostFunc(params.NewBranchName)
	}

	return fmt.Sprintf("successfully checkout to branch %s", params.NewBranchName), nil
}

func NewGitPushTool(repository git.FileSystem) *GitPushTool {
	return &GitPushTool{
		repository: repository,
	}
}

type GitPushTool struct {
	repository git.FileSystem
}

func (t *GitPushTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "git_push_tool",
		Desc: "git push工具，每当你完成一个任务后，都需要用此工具进行代码的推送",
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"branch_name": {
				Type:     "string",
				Desc:     "要推送的分支名字",
				Required: true,
			},
			"commit_message": {
				Type:     "string",
				Desc:     "本次提交的commit message",
				Required: true,
			},
		}),
	}, nil
}

func (t *GitPushTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	var params struct {
		BranchName    string `json:"branch_name"`
		CommitMessage string `json:"commit_message"`
	}
	if err := sonic.Unmarshal([]byte(argumentsInJSON), &params); err != nil {
		return "", errors.Wrapf(err, "method=sonic.Umarshal failed, failed to parse arguments, arguments=%s", argumentsInJSON)
	}
	if len(params.BranchName) <= 0 {
		return "", errors.New("invalid branch name")
	}
	_, err := t.repository.Commit(params.CommitMessage, "ai", "<EMAIL>")
	if err != nil {
		return "", errors.Wrapf(err, "method=git.FileSystem.Commit failed")
	}
	err = t.repository.Push(false)
	if err != nil {
		return "", errors.Wrapf(err, "method=git.FileSystem.Push failed")
	}

	return fmt.Sprintf("successfully push to branch %s", params.BranchName), nil
}
