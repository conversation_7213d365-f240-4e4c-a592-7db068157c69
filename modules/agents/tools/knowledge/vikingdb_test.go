package knowledge

import (
	"context"
	"fmt"
	"testing"

	"code.byted.org/stone/caribou/utils"
)

func TestInsert(t *testing.T) {
	ctx := context.Background()
	nodes := []Knowledge{
		{ID: 1, Content: "Test Content"},
	}
	err := Insert(ctx, nodes)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("Insert failed: %v", err)
	}
}

// go test -timeout 30s -v -run ^TestGetItemInfo$ code.byted.org/stone/caribou/modules/agents/tools/knowledge
func TestGetItemInfo(t *testing.T) {
	ctx := context.Background()
	ids := []uint64{15595255907488736171}
	items, err := GetItemInfo(ctx, ids)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("GetItemInfo failed: %v", err)
	}
	// if len(items) == 0 {
	// 	t.<PERSON><PERSON><PERSON>("GetItemInfo returned no items")
	// }
	fmt.Println(utils.Jsonify(items))
}

func TestSearch(t *testing.T) {
	ctx := context.Background()
	query := "Test query"
	ids, err := Search(ctx, query)
	if err != nil {
		t.Errorf("Search failed: %v", err)
	}
	if len(ids) == 0 {
		t.Errorf("Search returned no ids")
	}
	fmt.Println(ids)
}

func TestDelete(t *testing.T) {
	ctx := context.Background()
	// 这里建议先插入一条数据再删除，保证测试的健壮性。
	// 但如果已知某个ID一定存在，可以直接用。
	// 这里以1为例，实际可根据需要调整。
	ids := []uint64{15595255907488736171}
	err := Delete(ctx, ids)
	if err != nil {
		t.Errorf("Delete failed: %v", err)
	}
	// Verify the deletion by trying to get the deleted item
	items, err := GetItemInfo(ctx, ids)
	if err != nil {
		t.Errorf("GetItemInfo after deletion failed: %v", err)
	}
	if len(items) != 0 {
		t.Errorf("Item still exists after deletion")
	}
}
