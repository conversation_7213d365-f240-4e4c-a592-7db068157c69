package knowledge

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	vikingGO "code.byted.org/lagrange/viking_go_client"
	"code.byted.org/stone/caribou/utils"
)

// 参考源：https://code.byted.org/lagrange/viking_go_client/blob/master/vikingdb_client_test.go
// 线上库地址：https://byterec.bytedance.net/viking_db/detail/11611/model/155846?namespace=stone_caibou_recall&tab=basic
const (
	vikingDatabaseName = "stone_caibou_recall_1748401716__pkg_knowledge"
	vikingAccessToken  = "ea1b3e3e432d1e9ef0b450125f6a3b11"
)

type Knowledge struct {
	ID      uint64
	DSL     map[string]interface{} // dsl可以用来控制权限
	Content string
	Attrs   string
}

// AddNode 向viking中添加数据
func Insert(ctx context.Context, knowledgeList []Knowledge) error {
	vikingDataList := make([]*vikingGO.VikingDbData, 0)
	rawDataList := make([]*vikingGO.RawData, 0)
	for _, knowledge := range knowledgeList {
		data := map[string]interface{}{
			"text": knowledge.Content,
		}
		rawDataList = append(rawDataList, vikingGO.NewRawData(data, vikingGO.SetLabelByMD5()))

		dslInfo := make(map[string]interface{})
		// dslInfo["title"] = node.Title
		// dslInfo["content"] = node.Content
		// indexTime := time.Now().Format(`2006-01-02 15:04:05`)
		vikingDataList = append(vikingDataList, &vikingGO.VikingDbData{
			LabelLower: uint64(knowledge.ID),
			LabelUpper: 0,
			DslInfo:    dslInfo,
			Context:    []string{"default"},
			Attrs:      knowledge.Attrs,
		})
	}
	client := vikingGO.NewVikingDbClient(vikingDatabaseName, vikingAccessToken, vikingGO.Region_CN)
	rowKeys, logID, err := client.AddRawData(rawDataList, vikingDataList)
	if err != nil {
		return fmt.Errorf("client.AddRawData fail, knowledgeList:%+v rowKeys:%+v logID:%s err:%s",
			knowledgeList, rowKeys, logID, err)
	}
	// logs.CtxInfo(ctx, "client.AddRawData success, rowKeys:%+v logID:%s vikingDataList:\n%s", rowKeys, logID, utils.Jsonify(vikingDataList))
	return nil
}

// GetItemInfo 以ID来获取viking中的数据
func GetItemInfo(ctx context.Context, IDList []uint64) ([]Knowledge, error) {
	vkItemList := make([]Knowledge, 0, len(IDList))
	client := vikingGO.NewVikingDbClient(vikingDatabaseName, vikingAccessToken, vikingGO.Region_CN)
	labelList := make([]*vikingGO.LabelLowerAndUpper, 0, 0)
	for _, ID := range IDList {
		labelList = append(labelList, &vikingGO.LabelLowerAndUpper{LabelLower64: ID, LabelUpper64: 0})
	}
	result, err := client.GetData(labelList)
	if err != nil {
		return vkItemList, fmt.Errorf("client.GetData fail, itemId:%v err:%s", IDList, err)
	}
	for _, item := range result {
		// logs.CtxInfo(ctx, "item:%s", utils.Jsonify(item))
		// logs.CtxInfo(ctx, "type:%s", reflect.TypeOf(item["label_lower64"]))
		label, ok := item["label"].(string)
		if !ok {
			err = fmt.Errorf("label_lower64 key fail, item:%s", utils.Jsonify(item))
			return vkItemList, err
		} else if label == "" {
			continue
		}
		idList := strings.Split(label, "-")
		if len(idList) != 2 {
			err = fmt.Errorf("label invalid, label:%s item:%s", label, utils.Jsonify(item))
			return vkItemList, err
		}
		id, err := strconv.ParseUint(idList[1], 10, 64)
		if err != nil {
			return vkItemList, fmt.Errorf("ParseInt fail, label:%s item:%s", label, utils.Jsonify(item))
		}
		attrs, ok := item["attrs"].(string)
		if !ok {
			attrs = ""
		}
		rawData, ok := item["raw_data"].(map[string]interface{})
		if !ok {
			err = fmt.Errorf("raw_data key fail, item:%s", utils.Jsonify(item))
			return vkItemList, err
		}
		text, ok := rawData["text"].(string)
		if !ok {
			err = fmt.Errorf("text key fail, item:%s", utils.Jsonify(item))
			return vkItemList, err
		}
		vkItemList = append(vkItemList, Knowledge{
			ID:      id,
			Content: text,
			Attrs:   attrs,
		})
	}

	// logs.CtxInfo(ctx, "itemIDList:%v vkItemList:%s", IDList, utils.Jsonify(vkItemList))

	return vkItemList, nil
}

// Search 向量召回
func Search(ctx context.Context, query string) (IDList []uint64, err error) {
	data := map[string]interface{}{
		"text": query,
	}
	rawData := vikingGO.NewRawData(data, vikingGO.SetLabelByMD5())
	rawDataList := []*vikingGO.RawData{rawData}
	client := vikingGO.NewVikingDbClient(vikingDatabaseName, vikingAccessToken, vikingGO.Region_CN)
	embeddings, logID, vkErr := client.RawEmbedding(rawDataList)
	if vkErr != nil || len(embeddings) == 0 {
		err = fmt.Errorf("len(embeddings):%d logID:%s err:%v", len(embeddings), logID, vkErr)
		return
	}
	req := &vikingGO.RecallRequest{
		Embedding: embeddings[0],
		Index:     "v0", //模型详情页对应的version字段值
		SubIndex:  "default",
		TopK:      5,
		// DslInfo:   map[string]interface{}{
		// "op":    "range",
		// "field": "test_int64",
		// "gte":   1.0,
		// },
	}
	rsp, _, vkErr := client.Recall(req)
	if vkErr != nil {
		err = fmt.Errorf("client.Recall fail, query:[%s] logID:%s err:%v", query, logID, vkErr)
		return
	} else {
		for _, res := range rsp.Result {
			IDList = append(IDList, res.LabelLower64)
		}
	}

	return
}

// Delete 删除指定ID的数据
func Delete(ctx context.Context, idList []uint64) error {
	client := vikingGO.NewVikingDbClient(vikingDatabaseName, vikingAccessToken, vikingGO.Region_CN)
	labs := make([]*vikingGO.LabelLowerAndUpper, 0, len(idList))
	for _, id := range idList {
		lab := &vikingGO.LabelLowerAndUpper{
			LabelLower64: id,
			LabelUpper64: 0,
		}
		labs = append(labs, lab)
	}
	rowkeys, logID, err := client.DeleteData(labs)
	if err != nil {
		return fmt.Errorf("DeleteData fail, idList:%v rowkeys:%v logID:%s err:%v", idList, rowkeys, logID, err)
	}
	return nil
}
