package knowledge

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"

	"github.com/cloudwego/eino-ext/components/model/openai"
	"github.com/cloudwego/eino/components/prompt"
	"github.com/cloudwego/eino/compose"
	"github.com/cloudwego/eino/schema"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/stone/caribou/modules/agents/conf"
	"code.byted.org/stone/caribou/modules/agents/gen_klg_agent"
)

func RunSearchKlgAgent(ctx context.Context, query string) (chunkList []Knowledge, err error) {
	conf.MustInit()
	config := conf.LLMConf.ModelProvider.GPT4o
	cm, err := openai.NewChatModel(ctx, &openai.ChatModelConfig{
		APIKey:      config.AK,
		BaseURL:     config.BaseURL,
		APIVersion:  config.APIVersion,
		Model:       config.ModelName,
		MaxTokens:   conv.IntPtr(config.MaxToken),
		Temperature: conv.Float32Ptr(config.Temperature),
		TopP:        conv.Float32Ptr(config.TopP),
		ByAzure:     config.IsAzure,
	})
	if err != nil {
		return
	}

	// // 定义一个State，存储节点之间共享的数据
	// type globalState struct {
	// 	queries        []string
	// 	knowledgeList  []knowledge.Knowledge
	// 	rerankInput    string
	// 	rerankInputMap map[int]uint64
	// }
	rerankInputMap := make(map[int]uint64)
	knowledgeList := make([]Knowledge, 0)
	queries := make([]string, 0)
	chain := compose.NewChain[map[string]any, []Knowledge]()
	// 1. 执行query的rewrite
	// rewritedQueriesToState := func(ctx context.Context, out string, state *globalState) (string, error) {
	// 	state.ms = append(state.ms, out)
	// 	return out, nil
	// }
	rewriteChain := compose.NewChain[map[string]any, []string]().
		AppendChatTemplate(prompt.FromMessages(schema.FString, schema.SystemMessage(RewriteQuerySystemPrompt), schema.UserMessage(query))).
		AppendChatModel(cm).
		AppendLambda(compose.InvokableLambda(func(ctx context.Context, m *schema.Message) ([]string, error) {
			queries, err = extractQueriesFromJson(m.Content)
			if err != nil {
				logs.CtxError(ctx, "failed to extract queries from json: %v, content:\n%s", err, m.Content)
				queries = []string{query}
			}
			// 把原始query加入到queries的第0个位置
			queries = append([]string{query}, queries...)
			return queries, nil
		}))
	// 4. 如果有rerank后的结果，则返回rerank后的结果，否则进入实时搜索环节
	rerankChain := compose.NewChain[map[string]any, []Knowledge]().
		AppendChatTemplate(prompt.FromMessages(schema.FString, schema.SystemMessage(RerankKnowledgeSystemPrompt), schema.UserMessage("{rerankInput}"))).
		AppendChatModel(cm).
		AppendLambda(compose.InvokableLambda(func(ctx context.Context, m *schema.Message) ([]Knowledge, error) {
			rankList, err := extractRankFromJson(m.Content)
			logs.CtxInfo(ctx, "err:%v, rerank result is:\n%s rankList:\n%v", err, m.Content, rankList)
			if err != nil {
				logs.CtxError(ctx, "failed to extract queries from json: %v, content:\n%s", err, m.Content)
				return nil, err
			}
			retChunkList := []Knowledge{}
			for _, rank := range rankList {
				if Id, ok := rerankInputMap[rank]; ok {
					for _, k := range knowledgeList {
						if k.ID == Id {
							retChunkList = append(retChunkList, k)
							break
						}
					}
				}
			}
			return retChunkList, nil
		}))

	chain.
		AppendGraph(rewriteChain).
		AppendLambda(compose.InvokableLambda(func(ctx context.Context, queries []string) ([]Knowledge, error) {
			// 2. 分别调用生成的query执行搜索
			knowledgeList, err = searchKnowledge(ctx, queries)
			return knowledgeList, err
		})).
		AppendLambda(compose.InvokableLambda(func(ctx context.Context, knowledgeList []Knowledge) (map[string]any, error) {
			// 3. 将搜索结果交给LLM进行rerank
			rerankInput := ""
			rerankInput, rerankInputMap = transformKnowledgeListToString(queries, knowledgeList)
			logs.CtxInfo(ctx, "rerankInput is:\n%s", rerankInput)
			return map[string]any{
				"rerankInput":    rerankInput,
				"rerankInputMap": rerankInputMap,
			}, nil
		})).
		AppendGraph(rerankChain)

	// compile
	r, err := chain.Compile(ctx)
	if err != nil {
		log.Panicf("failed to compile chain: %v", err)
		return
	}

	output, err := r.Invoke(ctx, map[string]any{})
	if err != nil {
		log.Panicf("failed to invoke chain: %v", err)
		return
	}
	realSearchWhenEmpty := true
	if len(output) == 0 && realSearchWhenEmpty {
		answer, err := gen_klg_agent.RunGenKlgAgent(ctx, query)
		if err != nil {
			logs.CtxError(ctx, "failed to generate answer: %v", err)
			return nil, err
		}
		output = []Knowledge{
			{
				ID:      0,
				Content: answer,
			},
		}
	}
	logs.Flush()

	return output, nil
}

// 提取```json```中的query数组
func extractQueriesFromJson(content string) ([]string, error) {
	queries := []string{}
	if strings.Contains(content, "```json") {
		jsonStr := strings.Split(content, "```json")[1]
		jsonStr = strings.Split(jsonStr, "```")[0]
		err := json.Unmarshal([]byte(jsonStr), &queries)
		if err != nil {
			return nil, err
		}
	}
	return queries, nil
}

func extractRankFromJson(content string) ([]int, error) {
	rankList := []int{}
	if strings.Contains(content, "```json") {
		jsonStr := strings.Split(content, "```json")[1]
		jsonStr = strings.Split(jsonStr, "```")[0]
		err := json.Unmarshal([]byte(jsonStr), &rankList)
		if err != nil {
			return nil, err
		}
	}
	return rankList, nil
}

func searchKnowledge(ctx context.Context, queries []string) ([]Knowledge, error) {
	allResults := make([]Knowledge, 0)
	idCounts := make(map[uint64]int)

	// Search for each query
	for _, query := range queries {
		results, err := searchVikingdb(ctx, query)
		if err != nil {
			logs.CtxError(ctx, "failed to search vikingdb: %v", err)
			continue
		}

		// Count occurrences of each ID
		for _, result := range results {
			idCounts[result.ID]++
		}

		allResults = append(allResults, results...)
	}

	// Store occurrence counts in DSL
	uniqueResults := make([]Knowledge, 0)
	seenIDs := make(map[uint64]bool)

	for _, result := range allResults {
		if !seenIDs[result.ID] {
			if result.DSL == nil {
				result.DSL = make(map[string]interface{})
			}
			result.DSL["occurrence_count"] = idCounts[result.ID]
			uniqueResults = append(uniqueResults, result)
			seenIDs[result.ID] = true
		}
	}

	return uniqueResults, nil
}

func transformKnowledgeListToString(queries []string, knowledgeList []Knowledge) (string, map[int]uint64) {
	outputString := "<query_list>\n"
	outputString += fmt.Sprintf("原始query：%s\n", queries[0])
	if len(queries) > 1 {
		outputString += "扩展query：\n"
		queriesByte, err := json.Marshal(queries[1:])
		if err == nil {
			outputString += string(queriesByte)
		} else {
			outputString += fmt.Sprintf("%s", queries)
		}
	}
	outputString += "\n</query_list>\n"
	outputString += "<knowledge_list>\n"
	indexMap := make(map[int]uint64)
	for i, knowledge := range knowledgeList {
		indexMap[i] = knowledge.ID
		outputString += "<knowledge>\n"
		metaString := fmt.Sprintf("<meta>序号: %d</meta>\n", i)
		contentString := fmt.Sprintf("<content>%s</content>\n", knowledge.Content)
		outputString += metaString + contentString
		outputString += "</knowledge>\n\n"
	}
	outputString += "</knowledge_list>\n"
	return outputString, indexMap
}
