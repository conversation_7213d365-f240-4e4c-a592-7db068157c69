package knowledge

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
)

// ToolQueryKnowledge 实现了查询知识库的工具接口。
type ToolQueryKnowledge struct{}

// QueryKnowledgeParams 定义了调用知识查询工具所需的参数。
type QueryKnowledgeParams struct {
	Query string `json:"query"`
}

// GetKnowledgeQueryTool 是一个工厂函数，用于创建 ToolQueryKnowledge 的实例。
// 它返回一个 tool.InvokableTool 接口，可以被 eino 框架使用。
func GetKnowledgeQueryTool() tool.InvokableTool {
	return &ToolQueryKnowledge{}
}

// Info 方法返回工具的元数据，包括名称、描述和参数定义。
// 这些信息会被 eino 框架用于理解和调用此工具。
func (t *ToolQueryKnowledge) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: "query_knowledge",
		Desc: "根据用户输入查询知识库，返回相关的知识片段数组。",
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"query": {
				Type:     "string",
				Desc:     "用于搜索知识库的查询字符串。",
				Required: true,
			},
		}),
	}, nil
}

// InvokableRun 是工具的核心执行逻辑。
// 它接收 JSON 格式的参数字符串，执行查询，并返回 JSON 格式的结果字符串。
func (t *ToolQueryKnowledge) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (string, error) {
	var params QueryKnowledgeParams
	if err := json.Unmarshal([]byte(argumentsInJSON), &params); err != nil {
		return "", fmt.Errorf("failed to parse arguments for query_knowledge: %w", err)
	}

	if params.Query == "" {
		// 虽然 schema 中定义了 query 是必需的，这里再加一层校验以防万一。
		return "", fmt.Errorf("query parameter cannot be empty for query_knowledge")
	}

	items, err := RunSearchKlgAgent(ctx, params.Query)
	if err != nil {
		return "", err
	}

	resultJSON, err := json.Marshal(items)
	if err != nil {
		return "", fmt.Errorf("failed to marshal knowledge query results to JSON: %w", err)
	}

	return string(resultJSON), nil
}

// 注意:
// 1. Knowledge 结构体（包含 ID, Content, Attrs 等字段）应已在当前 'knowledge' 包中的其他文件（如 vikingdb.go）中定义。
//    如果需要特定的 JSON 字段名（例如小写），则应在该结构体定义中添加 `json:"fieldName"` 标签。
//    当前实现将使用 Knowledge 结构体字段的原始名称（如 "ID", "Content"）进行 JSON 序列化。
// 2. Search 和 GetItemInfo 函数也应在当前 'knowledge' 包中定义并可访问。

func searchVikingdb(ctx context.Context, query string) ([]Knowledge, error) {
	ids, err := Search(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("knowledge search failed: %w", err)
	}

	if len(ids) == 0 {
		// 如果没有找到任何匹配的 ID，返回一个空的 JSON 数组字符串。对于viking来说一般不可能。
		return []Knowledge{}, nil
	}

	// 调用在 vikingdb.go (同 knowledge 包下) 定义的 GetItemInfo 函数
	// GetItemInfo 返回 []Knowledge，其中 Knowledge 结构体也在 vikingdb.go 中定义。
	items, err := GetItemInfo(ctx, ids)
	if err != nil {
		return nil, fmt.Errorf("failed to get knowledge item info: %w", err)
	}
	return items, nil
}
