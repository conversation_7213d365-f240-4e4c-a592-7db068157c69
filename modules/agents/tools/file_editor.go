package tools

import (
	"context"
	"fmt"

	"github.com/bytedance/sonic"
	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/schema"
	"github.com/pkg/errors"
	"path/filepath"

	"code.byted.org/stone/caribou/pkg/git"
	"code.byted.org/stone/caribou/utils/file_util"
)

// todo 要优化这个tool

const (
	NameReadFileTool   = "read_file"
	NameCreateFileTool = "create_file"
	NameEditFileTool   = "edit_file"
)

var _ tool.InvokableTool = &ReadFileTool{}
var _ tool.InvokableTool = &CreateFileTool{}
var _ tool.InvokableTool = &EditFileTool{}

func NewReadFileTool(repository file_util.FileSystem) *ReadFileTool {
	return &ReadFileTool{
		repository: repository,
	}
}

type ReadFileTool struct {
	repository file_util.FileSystem
}

func (t *ReadFileTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: NameReadFileTool,
		Desc: "读取代码的工具，你可以从这个工具里读取出当前需要的代码",
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"type": {
				Type:     schema.String,
				Desc:     "read_file代表需要读取某个文件的代码, read_dir代表需要读取某个目录下的代码",
				Required: true,
			},
			"file_paths": {
				Type:     schema.Array,
				Desc:     "需要读取的文件路径list",
				Required: false,
				ElemInfo: &schema.ParameterInfo{
					Type:     schema.String,
					Desc:     "需要读取的文件路径，注意不要使用相对路径，而是使用绝对路径",
					Required: true,
				},
			},
		}),
	}, nil
}

func (t *ReadFileTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (result string, err error) {
	defer func() {
		if err != nil {
			result = fmt.Sprintf("failed to read file, err=%s", err.Error())
			err = nil
		}
	}()

	var param struct {
		Type      string   `json:"type"`
		FilePaths []string `json:"file_paths"`
	}
	const (
		readFile = "read_file"
		readDir  = "read_dir"
	)
	if err := sonic.Unmarshal([]byte(argumentsInJSON), &param); err != nil {
		return "", errors.Wrapf(err, "method=sonic.Umarshal failed, failed to parse arguments, arguments=%s", argumentsInJSON)
	}
	if param.Type == readFile {
		return file_util.ReadTargetProjectFiles(ctx, t.repository, param.FilePaths)
	} else if param.Type == readDir {
		return file_util.ReadTargetProjectDir(ctx, t.repository, param.FilePaths)
	}

	return "", errors.New("invalid params")
}

func NewCreateFileTool(repository git.FileSystem) *CreateFileTool {
	return &CreateFileTool{
		repository: repository,
	}
}

type CreateFileTool struct {
	repository git.FileSystem
}

type CreateFileToolParams struct {
	Path    string `json:"path"`
	Content string `json:"content"`
}

func (t *CreateFileTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: NameCreateFileTool,
		Desc: "This is a tool for creating a single file.\nBefore using this tool:\n1. Verify the directory path is correct\n2. Prepare the complete content for the file\n\nKey features of this tool:\n1. Creates only one file at a time for precise operations\n2. Automatically creates necessary directories in the file path\n3. Provides detailed success/failure information\n\nWhen creating a file:\n- Always use absolute file paths (paths will have leading / automatically trimmed)\n- Ensure the file content is complete and valid\n- Include all necessary dependencies, imports, and code\n- Make sure the code is idiomatic and follows the project conventions\n\nCommon use cases:\n1. Creating a new source file\n2. Adding configuration files\n3. Creating documentation files\n4. [Very Important] Output path before file content",
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"path": {
				Type:     schema.String,
				Desc:     "The absolute path to the file to create. Please output the path before the file content.",
				Required: true,
			},
			"content": {
				Type:     schema.String,
				Desc:     "The complete content of the file to be created. Please output the file content after the path.",
				Required: true,
			},
		}),
	}, nil
}

func (t *CreateFileTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (result string, err error) {
	defer func() {
		if err != nil {
			result = fmt.Sprintf("failed to create file, err=%s", err.Error())
			err = nil
		}
	}()

	var params CreateFileToolParams
	if err := sonic.Unmarshal([]byte(argumentsInJSON), &params); err != nil {
		return "", errors.Wrapf(err, "method=sonic.Umarshal failed, failed to parse arguments, arguments=%s", argumentsInJSON)
	}
	if len(params.Path) <= 0 || len(params.Content) <= 0 {
		return fmt.Sprintf("invalid params"), errors.New("invalid params")
	}

	// 确保目录存在
	dir := filepath.Dir(params.Path)
	if err := t.repository.MkdirAll(dir); err != nil {
		return "", errors.Wrapf(err, "method=git.FileSystem.MkdirAll failed, failed to create directory=%s", dir)
	}
	//content = edit.ReplaceImageUrlWithSign(ctx, content)
	//content, _, err := edit.EnsureValidSyntax(ctx, path, content)
	// 创建文件并写入内容
	if err := t.repository.WriteFile(params.Path, []byte(params.Content)); err != nil {
		return "", errors.Wrapf(err, "method=git.FileSystem.WriteFile failed, failed to write file=%s", params.Path)
	}

	return fmt.Sprintf("successfully created file: %s", params.Path), nil
}

func NewEditFileTool(repository git.FileSystem) *EditFileTool {
	return &EditFileTool{
		repository: repository,
	}
}

type EditFileTool struct {
	repository git.FileSystem
}

type EditFileToolParams struct {
	Path      string `json:"path"`
	StartLine int    `json:"start_line"`
	EndLine   int    `json:"end_line"`
	OldString string `json:"old_string"`
	NewString string `json:"new_string"`
}

func (t *EditFileTool) Info(ctx context.Context) (*schema.ToolInfo, error) {
	return &schema.ToolInfo{
		Name: NameEditFileTool,
		Desc: "This is a tool for editing files. \n\nBefore using this tool:\n1. Understand the file's latest contents and context. Users may provide currently opened files in their input, allowing you to directly access the required file content. If the file you want to read is not available in the context or has been modified, please use the read tool to obtain the most up-to-date file content.\n2. Verify the directory path is correct (only applicable when creating new files)\n\nThe tool will replace ONE occurrence of old_string with new_string in the specified file.\n\nCRITICAL REQUIREMENTS FOR USING THIS TOOL:\n\n1. UNIQUENESS: The old_string MUST uniquely identify the specific instance you want to change. This means:\n- Include AT LEAST 3-5 lines of context BEFORE the change point\n- Include AT LEAST 3-5 lines of context AFTER the change point\n- Include all whitespace, indentation, and surrounding code exactly as it appears in the file\n\n2. SINGLE INSTANCE: This tool can only change ONE instance at a time. If you need to change multiple instances:\n- Make separate calls to this tool for each instance\n- Each call must uniquely identify its specific instance using extensive context\n\n3. VERIFICATION: Before using this tool:\n- Check how many instances of the target text exist in the file\n- If multiple instances exist, gather enough context to uniquely identify each one\n- Plan separate tool calls for each instance\n\nWARNING: If you do not follow these requirements:\n- The tool will fail if old_string matches multiple locations\n- The tool will fail if old_string doesn't match exactly (including whitespace)\n- You may change the wrong instance if you don't include enough context\n\nWhen making edits:\n- Ensure the edit results in idiomatic, correct code\n- Do not leave the code in a broken state\n\nRemember: when making multiple file edits in a row to the same file, you should prefer to send all edits in a single message with multiple calls to this tool, rather than multiple messages with a single call each.\n[Very Important] Please output the path\\start_line\\end_line before old_string\\new_string in order.\n[Very Important] If file editing consistently fails, please use the create_file tool to override the entire file. ",
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"path": {
				Type:     schema.String,
				Desc:     "The path to the file to edit, must output this value before other parameters.",
				Required: true,
			},
			"start_line": {
				Type:     schema.Number,
				Desc:     "The start line number (integer) of the old_string in the file (1-based). Must set this value before old_string or new_string.",
				Required: true,
			},
			"end_line": {
				Type:     schema.Number,
				Desc:     "The end line number (integer) of the old_string in the file (1-based). Must set this value before old_string or new_string.",
				Required: true,
			},
			"old_string": {
				Type:     schema.String,
				Desc:     "The content to be replaced, must be unique within the file and contain the complete content from the starting line to the ending line. DO NOT INCLUDE LINE NUMBER AT THE BEGINNING OF EACH LINE IN THE OLD_STRING.",
				Required: true,
			},
			"new_string": {
				Type:     schema.String,
				Desc:     "The new content to replace with. DO NOT INCLUDE LINE NUMBER AT THE BEGINNING OF EACH LINE IN THE NEW_STRING.",
				Required: true,
			},
		}),
	}, nil
}

func (t *EditFileTool) InvokableRun(ctx context.Context, argumentsInJSON string, opts ...tool.Option) (result string, err error) {
	defer func() {
		if err != nil {
			result = fmt.Sprintf("failed to edit file, err=%s", err.Error())
			err = nil
		}
	}()

	var params EditFileToolParams
	if err := sonic.Unmarshal([]byte(argumentsInJSON), &params); err != nil {
		return "", errors.Wrapf(err, "method=sonic.Umarshal failed, failed to parse arguments, arguments=%s", argumentsInJSON)
	}
	if len(params.Path) <= 0 {
		return fmt.Sprintf("path cannot be empty"), errors.New("invalid path")
	}
	if len(params.OldString) <= 0 {
		return fmt.Sprintf("old string cannot be empty"), errors.New("invalid old string")
	}
	if params.StartLine > params.EndLine {
		return "", errors.New("invalid start/end line")
	}

	// 读取文件内容
	replaced, err := t.repository.ReplaceContent(&file_util.ReplaceContentParams{
		FilePath:  params.Path,
		StartLine: params.StartLine,
		EndLine:   params.EndLine,
		OldString: params.OldString,
		NewString: params.NewString,
	})
	if err != nil {
		return "", errors.Wrapf(err, "method=FileSystem.ReplaceContent failed, failed to replace file %s", params.Path)
	}
	if !replaced {
		return fmt.Sprintf("failed to replace content, please check if the path, old_string, or start_line/end_line parameters are correct."), nil
	}
	newContent, err := file_util.ReadTargetProjectFiles(ctx, t.repository, []string{params.Path})
	if err != nil {
		return fmt.Sprintf("file %s has been edited", params.Path), nil
	}

	return fmt.Sprintf("file %s has been edited.\nmodified file content:\n%s", params.Path, newContent), nil
}
