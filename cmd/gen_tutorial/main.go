package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"

	"code.byted.org/gopkg/logs"
	tutorial_agent "code.byted.org/stone/caribou/modules/agents/tutorial_agent"
	"code.byted.org/stone/caribou/utils"
)

func usage() {
	fmt.Fprintf(os.<PERSON>derr, `用法:

  getRepo  -r <repo_url>                下载仓库到本地
  genTask  -p <本地仓库路径>             生成任务列表
  genTuto  -p <本地仓库路径> -q <指令>   生成教程

示例:
  ./gen_tutorial getRepo -r https://code.byted.org/gopkg/myredis.git
  ./gen_tutorial genTask -p /path/to/local/repo
  ./gen_tutorial genTuto -p /path/to/local/repo -q "如何使用HashModel?"
  ./gen_tutorial genTuto -p /path/to/local/repo # 此时调用go doc为该目录下每个实体创建一个readme
`)
}

func main() {
	if len(os.Args) < 2 {
		usage()
		os.Exit(1)
	}
	cmd := os.Args[1]

	// 只解析命令后的参数
	fs := flag.NewFlagSet(cmd, flag.ExitOnError)
	repo := fs.String("r", "", "仓库地址，例如：https://code.byted.org/gopkg/myredis.git")
	path := fs.String("p", "", "仓库在本地的绝对地址")
	query := fs.String("q", "", "用户指令，例如：符号：'HashModel'，目录：'/Users/<USER>/Work/GoProjects/gopkg/myredis'")
	fs.Parse(os.Args[2:])

	switch cmd {
	case "getRepo":
		if *repo == "" {
			fmt.Fprintln(os.Stderr, "getRepo模式下请使用-r指定仓库地址")
			usage()
			os.Exit(1)
		}
		repoPath, err := tutorial_agent.DownloadRepo(context.Background(), *repo)
		if err != nil {
			log.Fatalf("DownloadRepo error: %v", err)
		}
		fmt.Printf("repo path: %s\n", repoPath)
	case "genTask":
		if *path == "" {
			fmt.Fprintln(os.Stderr, "genTask模式下请使用-p指定本地仓库路径")
			usage()
			os.Exit(1)
		}
		tasks, err := tutorial_agent.GenerateRepoTasks(context.Background(), *path)
		if err != nil {
			log.Fatalf("GenerateRepoTasks error: %v", err)
		}
		fmt.Printf("%s", strings.Join(tasks, "\n-------------\n"))
		fmt.Printf("\n-------------\n")
		fmt.Printf("task count: %d\n", len(tasks))
	case "genTuto":
		if *path == "" {
			fmt.Fprintln(os.Stderr, "genTuto模式下请使用-p指定本地仓库路径")
			usage()
			os.Exit(1)
		}
		if *query != "" {
			newQuery := fmt.Sprintf("仓库地址：%s\n用户指令：%s", *path, *query)
			result, err := tutorial_agent.RunTutorialAgent(newQuery)
			if err != nil {
				log.Fatalf("RunTutorialAgent error: %v", err)
			}
			fmt.Println(result)
		} else {
			genCount := BatchGenTuto(context.Background(), *path)
			logs.CtxInfo(context.Background(), "生成文件总数: %d", genCount)
		}
        logs.Flush()
	default:
		fmt.Fprintln(os.Stderr, "未知命令: ", cmd)
		usage()
		os.Exit(1)
	}
}

func BatchGenTuto(ctx context.Context, path string) (genCount int) {
	pwd, err := os.Getwd()
	if err != nil {
		logs.CtxError(ctx, "获取当前目录失败: %v", err)
		return
	}
	logs.CtxInfo(ctx, "当前目录: %s", pwd)
	// 设置当前目录为pwd
	defer func() {
		if err := os.Chdir(pwd); err != nil {
			logs.CtxError(ctx, "切换目录失败: %v", err)
		}
	}()
	tasks, err := tutorial_agent.GenerateRepoTasks(context.Background(), path)
	if err != nil {
		logs.CtxError(ctx, "GenerateRepoTasks error: %v", err)
	}
	// 设置当前目录为pwd
	if err := os.Chdir(pwd); err != nil {
		logs.CtxError(ctx, "切换目录失败: %v", err)
		return
	}
	// 检查 readmeDir 目录是否存在
	readmeDir := "./readmeDir"
	if _, err := os.Stat(readmeDir); os.IsNotExist(err) {
		// 目录不存在,创建目录
		if err := os.MkdirAll(readmeDir, 0755); err != nil {
			logs.CtxError(ctx, "创建目录失败: %v", err)
			return
		}
	}
	sessionSign := utils.Hash256(path)[:8]
	// 提取path最后一个目录信息，要考虑到path中可能包含多个/，且最后一个字符可能是/
	lastDir := path[strings.LastIndex(path, "/")+1:]
	// 在readmeDir目录下创建一个以sessionSign命名的目录
	sessionDir := fmt.Sprintf("%s/%s_%s", readmeDir, lastDir, sessionSign)
	if _, err := os.Stat(sessionDir); os.IsNotExist(err) {
		// 目录不存在,创建目录
		if err := os.MkdirAll(sessionDir, 0755); err != nil {
			logs.CtxError(ctx, "创建目录失败: %v", err)
			return
		}
	}
	for _, task := range tasks {
		// 为task生成签名
		taskSign := utils.Hash256(task)[:8]
		// 创建文件
		mdFile := fmt.Sprintf("%s/%s.md", sessionDir, taskSign)
		if _, err := os.Stat(mdFile); !os.IsNotExist(err) {
			logs.CtxInfo(ctx, "文件已存在: %s，跳过任务:\n%s", mdFile, task)
			continue
		}
		newQuery := fmt.Sprintf("仓库地址：%s\n为下面内容生成markdown文档：\n%s", path, task)
		result, err := tutorial_agent.RunTutorialAgent(newQuery)
		if err != nil || result == "" {
			logs.CtxError(ctx, "RunTutorialAgent error: %v, result: [%s]", err, result)
			continue
		}
		// 设置当前目录为pwd
		if err := os.Chdir(pwd); err != nil {
			logs.CtxError(ctx, "切换目录失败: %v", err)
			return
		}
		// 写入文件
		if err := os.WriteFile(mdFile, []byte(result), 0644); err != nil {
			logs.CtxError(ctx, "写入文件失败: %v", err)
			continue
		}
		logs.CtxInfo(ctx, "生成文件[%d/%d]: %s", genCount, len(tasks), mdFile)
		genCount++
	}
	return genCount
}
