package main

import (
	"context"
	"fmt"
	"os"

	"github.com/spf13/cobra"

	"code.byted.org/stone/caribou/cmd/caribou_cli/commands"
	"code.byted.org/stone/caribou/modules/agents"
	"code.byted.org/stone/caribou/utils/log_util"
)

const (
	version = "v0.0.1"
)

var rootCmd = &cobra.Command{
	Use:   "caribou",
	Short: "A CLI tool for Caribou operations",
	Long:  `A command line interface tool for performing various operations in the Caribou project.`,
}

var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "Print the version number",
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Printf("Caribou CLI %s\n", version)
	},
}

func init() {
	rootCmd.AddCommand(versionCmd)
	commands.AddCommands(rootCmd)
}

func main() {
	agents.MustInit()
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	// 保证trace能够完成上报
	agents.Close(context.Background())
	log_util.Flush()
}
