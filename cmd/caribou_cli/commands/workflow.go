package commands

import (
	"code.byted.org/kite/kitutil"
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/spf13/cobra"

	"code.byted.org/stone/caribou/cmd/caribou_cli/commands/internal/workflow"
	"code.byted.org/stone/caribou/utils/log_util"
)

// WorkflowCmd represents the workflow command
var WorkflowCmd = &cobra.Command{
	Use:   "workflow",
	Short: "Workflow related commands",
	Long:  `Commands for executing workflows in the Caribou system.`,
}

// FirstWorkflowCmd represents the first workflow command
var FirstWorkflowCmd = &cobra.Command{
	Use:   "first",
	Short: "Execute the first workflow",
	Long:  `Execute the first workflow that generates code based on user input.`,
	Run: func(cmd *cobra.Command, args []string) {
		// 解析命令行选项
		opts, err := workflow.ParseCLIFlags(cmd)
		if err != nil {
			return
		}
		ctx := kitutil.NewCtxWithLogID(context.Background(), opts.LogID)
		// 控制自定义输出到文件描述符
		fd3 := os.NewFile(3, "output")
		if fd3 == nil {
			log_util.CtxError(ctx, "method=os.NewFile failed, err=invalid fd")
			return
		}
		defer fd3.Close()

		log_util.CtxInfo(ctx, "first workflow started")
		startTime := time.Now()
		defer func() {
			if verbose, _ := cmd.Flags().GetBool("verbose"); verbose {
				fmt.Println(fmt.Sprintf("Workflow completed, cost_msec=%v", time.Since(startTime).Milliseconds()))
			}
			log_util.CtxInfo(ctx, "first workflow finished")
		}()
		// 运行工作流
		result, err := workflow.RunWorkflowFromCLI(ctx, opts)
		if err != nil {
			log_util.CtxError(ctx, "method=workflow.RunWorkflowFromCLI failed, err=%+v", err)
			fd3.Write([]byte("failed"))
			return
		}
		editFiles := make(map[string]string, len(result.CodeDiff))
		for originPath, diff := range result.CodeDiff {
			path := strings.TrimPrefix(originPath, "/repo/")
			if path == originPath {
				path = strings.TrimPrefix(path, "repo/")
			}
			editFiles[path] = diff.Modified
		}
		editFilesStr, err := sonic.Marshal(editFiles)
		if err != nil {
			log_util.CtxError(ctx, "method=sonic.Marshal failed, err=%+v", err)
			fd3.Write([]byte("failed"))
			return
		}
		fd3.Write(editFilesStr)
	},
}

// 初始化命令行参数
func init() {
	// 使用工厂函数注册工作流命令行标志
	workflow.RegisterWorkflowFlags(FirstWorkflowCmd)
}
