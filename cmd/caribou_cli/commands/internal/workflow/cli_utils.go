package workflow

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cobra"

	"code.byted.org/stone/caribou/modules/agents/workflows"
	"code.byted.org/stone/caribou/pkg/git"
	"code.byted.org/stone/caribou/utils/log_util"
)

// CLIOptions 定义命令行选项
type CLIOptions struct {
	Input          string
	BaseBranchName string
	RepoURL        string
	RepoToken      string
	TosURL         string
	Verbose        bool
	LogID          string
}

// ParseCLIFlags 从cobra命令解析命令行标志
func ParseCLIFlags(cmd *cobra.Command) (*CLIOptions, error) {
	input, _ := cmd.Flags().GetString("input")
	baseBranchName, _ := cmd.Flags().GetString("base-branch")
	repoURL, _ := cmd.Flags().GetString("repo-url")
	repoToken, _ := cmd.Flags().GetString("repo-token")
	tosURL, _ := cmd.Flags().GetString("tos-url")
	verbose, _ := cmd.Flags().GetBool("verbose")
	logID, _ := cmd.Flags().GetString("logid")

	return &CLIOptions{
		Input:          input,
		BaseBranchName: baseBranchName,
		RepoURL:        repoURL,
		RepoToken:      repoToken,
		TosURL:         tosURL,
		Verbose:        verbose,
		LogID:          logID,
	}, nil
}

// ValidateCLIOptions 验证命令行选项
func ValidateCLIOptions(opts *CLIOptions) error {
	if opts == nil {
		return errors.New("options cannot be nil")
	}

	var missingFields []string
	if len(opts.Input) <= 0 {
		missingFields = append(missingFields, "input")
	}
	if len(opts.RepoURL) > 0 {
		if len(opts.RepoToken) <= 0 {
			missingFields = append(missingFields, "repo-token")
		}
	} else if len(opts.TosURL) <= 0 && len(opts.RepoURL) <= 0 {
		missingFields = append(missingFields, "repo-url")
	}
	if len(opts.RepoURL) > 0 && len(opts.BaseBranchName) <= 0 {
		missingFields = append(missingFields, "base-branch")
	}
	if len(missingFields) > 0 {
		return fmt.Errorf("missing required fields: %s", strings.Join(missingFields, ", "))
	}

	return nil
}

// RunWorkflowFromCLI 从命令行运行工作流
func RunWorkflowFromCLI(ctx context.Context, opts *CLIOptions) (*workflows.FirstWorkFlowResult, error) {
	if err := ValidateCLIOptions(opts); err != nil {
		return nil, err
	}

	startTime := time.Now()
	defer func() {
		log_util.CtxInfo(ctx, "Workflow completed, cost_msc=%v", time.Since(startTime).Milliseconds())
	}()
	// 记录详细日志
	if opts.Verbose {
		log_util.CtxInfo(ctx, "Running workflow with options: %+v", *opts)
	}
	// 获取repo
	gitClientParams := &git.NewGitClientParams{
		UseMemory: true,
	}
	if len(opts.RepoURL) > 0 {
		gitClientParams.Codebase = &git.CodebaseParams{
			URL:   opts.RepoURL,
			Token: opts.RepoToken,
		}
	}
	if len(opts.TosURL) > 0 {
		gitClientParams.Tos = &git.TosParams{
			TosURL: opts.TosURL,
		}
	}
	gitClient, err := git.NewGitClient(gitClientParams)
	if err != nil {
		return nil, errors.Wrapf(err, "method=git.NewGitClien failed")
	}
	repository, err := gitClient.GetFS(ctx)
	if err != nil {
		panic(err)
	}
	// 执行工作流
	workflow, err := workflows.NewFirstWorkflow(ctx)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to create FirstWorkflow")
	}
	result, err := workflow.Generate(ctx, &workflows.FirstWorkFlowParams{
		Input:          opts.Input,
		Repo:           repository,
		BaseBranchName: opts.BaseBranchName,
	})
	if err != nil {
		return nil, errors.Wrapf(err, "method=(*workflow.FirstWorkFlow).Generate failed")
	}

	return result, nil
}

// RegisterWorkflowFlags 注册工作流命令行标志
func RegisterWorkflowFlags(cmd *cobra.Command) {
	cmd.Flags().String("input", "", "User input for the workflow")
	cmd.Flags().String("base-branch", "main", "Base branch name")
	cmd.Flags().String("repo-url", "", "Repository URL")
	cmd.Flags().String("repo-token", "", "Repository access token")
	cmd.Flags().String("tos-url", "", "Tos URL")
	cmd.Flags().Bool("verbose", false, "Enable verbose logging")
	cmd.Flags().String("logid", "", "LogID")
}
