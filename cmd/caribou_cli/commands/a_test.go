package commands

import (
	"fmt"
	"github.com/spf13/cobra"
	"io"
	"os"
	"os/exec"
	"path"
	"strings"
	"testing"
)

func TestA(t *testing.T) {
	var rootCmd = &cobra.Command{
		Use:   "example",
		Short: "Example command",
		Run: func(cmd *cobra.Command, args []string) {
			fd3 := os.NewFile(3, "output")
			fd3.Write([]byte("Hello, World!"))
		},
	}

	// 执行命令并捕获输出
	stdout, stderr, err := ExecuteCommandWithCapture(rootCmd, []string{})

	if err != nil {
		fmt.Printf("Command failed: %v\n", err)
	}

	fmt.Printf("Stdout: %s", stdout)
	fmt.Printf("Stderr: %s", stderr)
}

func TestB(t *testing.T) {
	r, w, err := os.Pipe()
	if err != nil {
		panic(err)
	}
	defer r.Close()
	defer w.<PERSON>()
	cmd := exec.Command(path.Join("/Users/<USER>/go/src/code.byted.org/stone/caribou/bin", "caribou"), "agent", "demo", "hello")

	cmd.ExtraFiles = []*os.File{w}

	err = cmd.Start()
	if err != nil {
		panic(err)
	}
	err = cmd.Wait()
	w.Close()
	resp, err := io.ReadAll(r)
	if string(resp) == "failed" {
		fmt.Println("ok")
	}
	fmt.Println(string(resp))
}

func ExecuteCommandWithCapture(cmd2 *cobra.Command, args []string) (string, string, error) {
	// 执行命令
	cmd := exec.Command("example")
	r, w, err := os.Pipe()
	if err != nil {
		return "", "", err
	}
	defer r.Close()
	defer w.Close()

	// 设置文件描述符 3
	cmd.ExtraFiles = []*os.File{w}

	// 启动命令
	err = cmd.Start()
	if err != nil {
		return "", "", err
	}

	// 关闭写端，准备读取
	w.Close()

	// 读取 fd3 的内容
	output, err := io.ReadAll(r)
	if err != nil {
		return "", "", err
	}

	// 等待命令完成
	err = cmd.Wait()
	if err != nil {
		return "", "", err
	}

	return string(output), "", err
}

func TestC(t *testing.T) {
	originPath := "repo/src/index.html"
	path := strings.TrimPrefix(originPath, "/repo/")
	if path == originPath {
		path = strings.TrimPrefix(path, "repo/")
	}
	fmt.Println(path)
}
