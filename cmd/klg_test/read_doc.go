package main

import (
	"crypto/md5"
	"encoding/hex"
	"flag"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"path/filepath"

	"code.byted.org/gopkg/logs"
	htmlmd "github.com/JohannesKaufmann/html-to-markdown/v2"
)

func readDoc() {
	url := flag.String("url", "", "要读取的网页URL")
	flag.Parse()
	if *url == "" {
		log.Fatal("请使用-url参数指定要读取的网页URL")
	}

	// 计算url的md5签名
	h := md5.New()
	h.Write([]byte(*url))
	md5sign := hex.EncodeToString(h.Sum(nil))
	cacheDir := ".cache"
	cacheFile := filepath.Join(cacheDir, md5sign+".html")

	// 确保.cache目录存在
	if _, err := os.Stat(cacheDir); os.IsNotExist(err) {
		if err := os.Mkdir(cacheDir, 0755); err != nil {
			log.Fatalf("创建缓存目录失败: %v", err)
		}
	}

	var body []byte
	if _, err := os.Stat(cacheFile); err == nil {
		// 命中缓存
		body, err = ioutil.ReadFile(cacheFile)
		if err != nil {
			log.Fatalf("读取缓存失败: %v", err)
		} else {
			logs.Infof("命中缓存: %s", cacheFile)
		}
	} else {
		// 未命中缓存，下载网页
		resp, err := http.Get(*url)
		if err != nil {
			log.Fatalf("获取网页失败: %v", err)
		}
		defer resp.Body.Close()
		body, err = ioutil.ReadAll(resp.Body)
		if err != nil {
			log.Fatalf("读取网页内容失败: %v", err)
		}
		// 写入缓存
		if err := ioutil.WriteFile(cacheFile, body, 0644); err != nil {
			log.Printf("写入缓存失败: %v", err)
		}
	}

	markdown, err := htmlmd.ConvertString(string(body))
	if err != nil {
		log.Fatal(err)
	}
	logs.Infof("markdown:\n%s", markdown)
	logs.Flush()
}
