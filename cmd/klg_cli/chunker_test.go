package main

//
// import (
// 	"os"
// 	"testing"
//
// 	gen_klg_agent "code.byted.org/stone/caribou/modules/agents/gen_klg_agent"
// )
//
// func writeTempFile(t *testing.T, content string) string {
// 	t.Helper()
// 	f, err := os.CreateTemp("", "mdtest-*.md")
// 	if err != nil {
// 		t.Fatalf("无法创建临时文件: %v", err)
// 	}
// 	_, err = f.WriteString(content)
// 	if err != nil {
// 		f.Close()
// 		os.Remove(f.Name())
// 		t.Fatalf("写入临时文件失败: %v", err)
// 	}
// 	f.Close()
// 	return f.Name()
// }
//
// func TestChunkMarkdown_Basic(t *testing.T) {
// 	md := `# 标题1
// ## 标题2
// 内容2: aaa..aaa
// ### 标题3
// 内容3: bbb..bbb`
// 	file := writeTempFile(t, md)
// 	defer os.Remove(file)
// 	chunks, err := ChunkMarkdownByLevel(md)
// 	if err != nil {
// 		t.Fatalf("SplitMarkdown 错误: %v", err)
// 	}
// 	if len(chunks) != 2 {
// 		t.Errorf("期望2个分片，实际%d", len(chunks))
// 	}
// 	if chunks[0].Title != "标题1/标题2" || chunks[0].Content != "内容2: aaa..aaa" {
// 		t.Errorf("分片1不正确: %+v", chunks[0])
// 	}
// 	if chunks[1].Title != "标题1/标题2/标题3" || chunks[1].Content != "内容3: bbb..bbb" {
// 		t.Errorf("分片2不正确: %+v", chunks[1])
// 	}
// 	_, s1 := gen_klg_agent.MakeSign("标题1")
// 	_, s2 := gen_klg_agent.MakeSign("标题2")
// 	_, s3 := gen_klg_agent.MakeSign("标题3")
// 	if len(chunks[0].SignList) != 2 || chunks[0].SignList[0] != s1 || chunks[0].SignList[1] != s2 {
// 		t.Errorf("分片1 SignList 不正确: %+v", chunks[0].SignList)
// 	}
// 	if len(chunks[1].SignList) != 3 || chunks[1].SignList[0] != s1 || chunks[1].SignList[1] != s2 || chunks[1].SignList[2] != s3 {
// 		t.Errorf("分片2 SignList 不正确: %+v", chunks[1].SignList)
// 	}
// }
//
// func TestChunkMarkdown_EmptyFile(t *testing.T) {
// 	file := writeTempFile(t, "")
// 	defer os.Remove(file)
// 	chunks, err := ChunkMarkdown(file)
// 	if err != nil {
// 		t.Fatalf("SplitMarkdown 错误: %v", err)
// 	}
// 	if len(chunks) != 0 {
// 		t.Errorf("空文件应无分片，实际%d", len(chunks))
// 	}
// }
//
// func TestChunkMarkdown_NoContent(t *testing.T) {
// 	md := `# 只有标题1\n## 只有标题2`
// 	file := writeTempFile(t, md)
// 	defer os.Remove(file)
// 	chunks, err := ChunkMarkdown(file)
// 	if err != nil {
// 		t.Fatalf("ChunkMarkdown 错误: %v", err)
// 	}
// 	if len(chunks) != 0 {
// 		t.Errorf("无正文内容应无分片，实际%d", len(chunks))
// 	}
// }
//
// func TestChunkMarkdown_MultiLevel(t *testing.T) {
// 	md := `# 一级
// 内容1
// ## 二级
// 内容2
// ### 三级
// 内容3`
// 	file := writeTempFile(t, md)
// 	defer os.Remove(file)
// 	chunks, err := ChunkMarkdown(file)
// 	if err != nil {
// 		t.Fatalf("ChunkMarkdown 错误: %v", err)
// 	}
// 	if len(chunks) != 3 {
// 		t.Errorf("期望3个分片，实际%d", len(chunks))
// 	}
// 	if chunks[0].Title != "一级" || chunks[0].Content != "内容1" {
// 		t.Errorf("分片1不正确: %+v", chunks[0])
// 	}
// 	if chunks[1].Title != "一级/二级" || chunks[1].Content != "内容2" {
// 		t.Errorf("分片2不正确: %+v", chunks[1])
// 	}
// 	if chunks[2].Title != "一级/二级/三级" || chunks[2].Content != "内容3" {
// 		t.Errorf("分片3不正确: %+v", chunks[2])
// 	}
// 	_, s1 := gen_klg_agent.MakeSign("一级")
// 	_, s2 := gen_klg_agent.MakeSign("二级")
// 	_, s3 := gen_klg_agent.MakeSign("三级")
// 	if len(chunks[0].SignList) != 1 || chunks[0].SignList[0] != s1 {
// 		t.Errorf("分片1 SignList 不正确: %+v", chunks[0].SignList)
// 	}
// 	if len(chunks[1].SignList) != 2 || chunks[1].SignList[0] != s1 || chunks[1].SignList[1] != s2 {
// 		t.Errorf("分片2 SignList 不正确: %+v", chunks[1].SignList)
// 	}
// 	if len(chunks[2].SignList) != 3 || chunks[2].SignList[0] != s1 || chunks[2].SignList[1] != s2 || chunks[2].SignList[2] != s3 {
// 		t.Errorf("分片3 SignList 不正确: %+v", chunks[2].SignList)
// 	}
// }
//
