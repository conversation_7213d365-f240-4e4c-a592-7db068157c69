package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"strings"
	"time"

	"code.byted.org/gopkg/logs"
	"code.byted.org/stone/caribou/modules/agents/gen_klg_agent"
	"code.byted.org/stone/caribou/modules/agents/tools/knowledge"
)

func main() {
	if len(os.Args) < 2 {
		printUsage()
		os.Exit(1)
	}
	defer func() {
		logs.Flush()
	}()

	switch os.Args[1] {
	case "build":
		runBuild(os.Args[2:])
	case "chunk":
		runChunk(os.Args[2:])
	case "index":
		runIndex(os.Args[2:])
	case "recall":
		runRecall(os.Args[2:])
	case "query":
		runQuery(os.Args[2:])
	default:
		fmt.Fprintln(os.Stderr, "未知子命令: ", os.Args[1])
		printUsage()
		os.Exit(1)
	}
}

// build命令逻辑
func runBuild(args []string) {
	buildCmd := flag.NewFlagSet("build", flag.ExitOnError)
	input := buildCmd.String("i", "", "输入内容")
	output := buildCmd.String("o", "", "输出markdown文件名")
	jsonFile := buildCmd.String("f", "", "批量输入json文件")
	buildCmd.Parse(args)

	if *jsonFile == "" && (*input == "" || *output == "") {
		fmt.Fprintln(os.Stderr, "build 需要 -i 输入内容 和 -o 输出文件名，或 -f 批量输入json文件")
		printUsage()
		os.Exit(1)
	}

	if *jsonFile != "" {
		content, err := os.ReadFile(*jsonFile)
		if err != nil {
			fmt.Fprintf(os.Stderr, "读取json文件失败: %v\n", err)
			os.Exit(1)
		}
		klgBaseDir := "klg_base"
		if _, err := os.Stat(klgBaseDir); os.IsNotExist(err) {
			err = os.Mkdir(klgBaseDir, 0755)
			if err != nil {
				fmt.Fprintf(os.Stderr, "创建klg_base目录失败: %v\n", err)
				os.Exit(1)
			}
			fmt.Println("已创建klg_base目录")
		}
		var items []struct {
			Name        string   `json:"name"`
			Alias       []string `json:"alias"`
			Description string   `json:"description"`
			RelatedDocs []string `json:"related_docs"`
		}
		err = json.Unmarshal(content, &items)
		if err != nil {
			fmt.Fprintf(os.Stderr, "解析json失败: %v\n", err)
			os.Exit(1)
		}
		for i, item := range items {
			aliasStr := ""
			if len(item.Alias) > 0 {
				aliasStr = "/" + joinWithSlash(item.Alias)
			}
			docsStr := ""
			if len(item.RelatedDocs) > 0 {
				docsStr = joinDocs(item.RelatedDocs)
			}
			query := fmt.Sprintf("请撰写%s%s的技术文档。\n提示：%s\n参考文档：%s", item.Name, aliasStr, item.Description, docsStr)
			fileName := fmt.Sprintf("%s/%s.md", klgBaseDir, item.Name)
			if _, err := os.Stat(fileName); err == nil {
				logs.Infof("文件已存在，跳过: %s\n", fileName)
				continue
			}
			logs.Infof("生成: %s\nQuery: %s\n", fileName, query)
			ctx := context.Background()
			md, err := gen_klg_agent.RunGenKlgAgent(ctx, query)
			if err != nil {
				logs.Errorf("RunGenKlgAgent 失败: %v\n", err)
				continue
			}
			err = os.WriteFile(fileName, []byte(md), 0644)
			if err != nil {
				logs.Errorf("写入文件失败: %v\n", err)
				continue
			}
			logs.Infof("[%d/%d]已写入: %s\n", i+1, len(items), fileName)
		}
		return
	}

	ctx := context.Background()
	content, err := gen_klg_agent.RunGenKlgAgent(ctx, *input)
	if err != nil {
		fmt.Fprintf(os.Stderr, "RunGenKlgAgent failed: %v\n", err)
		os.Exit(1)
	}
	err = os.WriteFile(*output, []byte(content), 0644)
	if err != nil {
		fmt.Fprintf(os.Stderr, "写入markdown文件失败: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("已写入markdown文件: %s\n", *output)
}

// chunk命令逻辑
func runChunk(args []string) {
	chunkMdCmd := flag.NewFlagSet("chunk", flag.ExitOnError)
	file := chunkMdCmd.String("f", "", "markdown文件名")
	chunkMdCmd.Parse(args)
	if *file == "" {
		fmt.Fprintln(os.Stderr, "chunk 需要 -f 被chunk的markdown文件名")
		printUsage()
		os.Exit(1)
	}
	content, err := os.ReadFile(*file)
	if err != nil {
		fmt.Fprintf(os.Stderr, "读取markdown文件失败: %v\n", err)
		os.Exit(1)
	}
	chunks, err := ChunkMarkdownOverlap(string(content))
	if err != nil {
		fmt.Fprintf(os.Stderr, "ChunkMarkdownOverlap 失败: %v\n", err)
		os.Exit(1)
	}
	for i, chunk := range chunks {
		fmt.Printf("分片%d\nId:%d\n分片内容：\n%s\n\n", i+1, chunk.ID, chunk.Content)
		fmt.Printf("--------------------------------\n")
	}
}

// index命令逻辑
func runIndex(args []string) {
	indexMdCmd := flag.NewFlagSet("index", flag.ExitOnError)
	file := indexMdCmd.String("f", "", "需要被索引的markdown文件名")
	batchfile := indexMdCmd.String("ff", "", "文件名，文件内部是按行存放到markdown文件列表")
	indexMdCmd.Parse(args)
	if (*file == "" && *batchfile == "") || (*file != "" && *batchfile != "") {
		fmt.Fprintln(os.Stderr, "index 需要 -f 被索引的markdown文件名 或 -ff 文件名，文件内部是按行存放到markdown文件列表")
		printUsage()
		os.Exit(1)
	}
	if *batchfile != "" {
		content, err := os.ReadFile(*batchfile)
		if err != nil {
			fmt.Fprintf(os.Stderr, "读取批量文件失败: %v\n", err)
			os.Exit(1)
		}
		files := strings.Split(string(content), "\n")
		fileList := make([]string, 0, len(files))
		for _, file := range files {
			file = strings.TrimSpace(file)
			if file == "" {
				continue
			}
			fileList = append(fileList, file)
		}
		for i, file := range fileList {
			if knowledgeList, err := indexSingleFile(file); err != nil {
				fmt.Fprintf(os.Stderr, "索引文件失败: %v, file: %s\n", err, file)
				continue
			} else {
				logs.Infof("[%d/%d] chunk_count: %4d file: %s", i+1, len(fileList), len(knowledgeList), file)
			}
		}
	} else {
		if knowledgeList, err := indexSingleFile(*file); err != nil {
			fmt.Fprintf(os.Stderr, "索引文件失败: %v\n", err)
			os.Exit(1)
		} else {
			logs.Infof("chunk_count: %4d file: %s", len(knowledgeList), *file)
		}
	}
}

// 封装：索引单个markdown文件到知识库
func indexSingleFile(file string) ([]knowledge.Knowledge, error) {
	content, err := os.ReadFile(file)
	if err != nil {
		return nil, fmt.Errorf("读取markdown文件失败: %w", err)
	}
	chunks, err := ChunkMarkdownOverlap(string(content))
	if err != nil {
		return nil, fmt.Errorf("ChunkMarkdownOverlap 失败: %w", err)
	}
	ctx := context.Background()
	var knowledgeList []knowledge.Knowledge
	indexTime := time.Now().Format(`2006-01-02 15:04:05`)
	attrsMap := make(map[string]any)
	attrsMap["index_time"] = indexTime
	attrsMap["file_name"] = file
	for _, chunk := range chunks {
		attrsJSON, err := json.Marshal(attrsMap)
		if err != nil {
			return nil, fmt.Errorf("json.Marshal 失败: %w", err)
		}
		knowledgeList = append(knowledgeList, knowledge.Knowledge{
			ID:      chunk.ID,
			Content: chunk.Content,
			Attrs:   string(attrsJSON),
		})
	}
	err = knowledge.Insert(ctx, knowledgeList)
	if err != nil {
		return nil, fmt.Errorf("knowledge.Insert 失败: %w", err)
	}
	return knowledgeList, nil
}

// recall命令逻辑
func runRecall(args []string) {
	recallCmd := flag.NewFlagSet("recall", flag.ExitOnError)
	queryStr := recallCmd.String("q", "", "搜索查询字符串")
	filePath := recallCmd.String("f", "", "批量查询文件，内容用------分割")
	recallCmd.Parse(args)

	if *filePath == "" && *queryStr == "" {
		fmt.Fprintln(os.Stderr, "query 需要 -q 搜索查询字符串 或 -f 查询文件")
		printUsage()
		os.Exit(1)
	}

	ctx := context.Background()
	var queries []string
	if *filePath != "" {
		content, err := os.ReadFile(*filePath)
		if err != nil {
			fmt.Fprintf(os.Stderr, "读取查询文件失败: %v\n", err)
			os.Exit(1)
		}
		queries = splitQueries(string(content))
	} else {
		queries = []string{*queryStr}
	}

	if len(queries) > 1 && *filePath != "" {
		// 多条查询，写入文件
		timestamp := time.Now().Format("20060102-150405")
		outputFile := fmt.Sprintf("%s-%s.txt", *filePath, timestamp)
		var builder strings.Builder
		for _, q := range queries {
			q = trimQuery(q)
			if q == "" {
				continue
			}
			builder.WriteString(q)
			builder.WriteString("\n--------------------------------\n")
			// ids, err := knowledge.Search(ctx, q)
			// if err != nil {
			// 	builder.WriteString(fmt.Sprintf("搜索失败: %v\n================================\n", err))
			// 	continue
			// }
			// if len(ids) == 0 {
			// 	builder.WriteString("未找到匹配的内容\n================================\n")
			// 	continue
			// }
			// items, err := knowledge.GetItemInfo(ctx, ids)
			// if err != nil {
			// 	builder.WriteString(fmt.Sprintf("获取内容详情失败: %v\n================================\n", err))
			// 	continue
			// }
			items, err := knowledge.RunSearchKlgAgent(ctx, q)
			if err != nil {
				builder.WriteString(fmt.Sprintf("搜索失败: %v\n================================\n", err))
				continue
			}
			if len(items) == 0 {
				builder.WriteString("未找到匹配的内容\n================================\n")
				continue
			}
			for i, item := range items {
				// 只显示前5条
				if i > 4 {
					break
				}
				builder.WriteString(fmt.Sprintf("%d\n--------------------------------\n%s\n--------------------------------\n%s", item.ID, item.Attrs, item.Content))
				if i != len(items)-1 {
					builder.WriteString("\n++++++++++++++++++++++++++++++++\n")
				}
			}
			builder.WriteString("\n================================\n")
		}
		err := os.WriteFile(outputFile, []byte(builder.String()), 0644)
		if err != nil {
			fmt.Fprintf(os.Stderr, "写入查询结果文件失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Printf("多条查询结果已写入: %s\n", outputFile)
		return
	}

	// 单条查询，原有逻辑
	for _, q := range queries {
		q = trimQuery(q)
		if q == "" {
			continue
		}
		fmt.Printf("%s\n--------------------------------\n", q)
		ids, err := knowledge.Search(ctx, q)
		if err != nil {
			fmt.Printf("搜索失败: %v\n================================\n", err)
			continue
		}
		if len(ids) == 0 {
			fmt.Printf("未找到匹配的内容\n================================\n")
			continue
		}
		items, err := knowledge.GetItemInfo(ctx, ids)
		if err != nil {
			fmt.Printf("获取内容详情失败: %v\n================================\n", err)
			continue
		}
		for i, item := range items {
			fmt.Printf("%d\n--------------------------------\n%s\n--------------------------------\n%s", item.ID, item.Attrs, item.Content)
			if i != len(items)-1 {
				fmt.Printf("\n++++++++++++++++++++++++++++++++\n")
			}
		}
		fmt.Printf("\n================================\n")
	}
}

func runQuery(args []string) {
	queryCmd := flag.NewFlagSet("query", flag.ExitOnError)
	queryStr := queryCmd.String("q", "", "搜索查询字符串")
	queryCmd.Parse(args)

	if *queryStr == "" {
		fmt.Fprintln(os.Stderr, "query 需要 -q 搜索查询字符串")
		printUsage()
		os.Exit(1)
	}

	ctx := context.Background()
	answer, err := knowledge.RunSearchKlgAgent(ctx, *queryStr)
	if err != nil {
		fmt.Fprintf(os.Stderr, "查询失败: %v\n", err)
		os.Exit(1)
	}
	fmt.Println(answer)
}

// 工具函数
func joinWithSlash(arr []string) string {
	if len(arr) == 0 {
		return ""
	}
	res := arr[0]
	for i := 1; i < len(arr); i++ {
		res += "/" + arr[i]
	}
	return res
}

func joinDocs(docs []string) string {
	if len(docs) == 0 {
		return ""
	}
	res := docs[0]
	for i := 1; i < len(docs); i++ {
		res += ", " + docs[i]
	}
	return res
}

// 新增：分割和去除多余空白的工具函数
func splitQueries(content string) []string {
	var res []string
	sep := "------"
	parts := make([]string, 0)
	start := 0
	for i := 0; i+len(sep) <= len(content); {
		if content[i:i+len(sep)] == sep {
			parts = append(parts, content[start:i])
			i += len(sep)
			start = i
		} else {
			i++
		}
	}
	parts = append(parts, content[start:])
	for _, p := range parts {
		if trimQuery(p) != "" {
			res = append(res, p)
		}
	}
	return res
}

func trimQuery(q string) string {
	return strings.TrimSpace(q)
}
func printUsage() {
	fmt.Fprintf(os.Stderr, `示例:
  gen_klg build  -i "请撰写关于tos的文档。带上golang的代码示例。不要使用imagex和volcengine的方案" -o tos.md # 单条内容生成markdown
  gen_klg build  -f batch_input.json # 批量内容生成markdown，json文件内为内容列表
  gen_klg chunk  -f tos.md # 对markdown文件进行分片
  gen_klg index  -f tos.md # 索引单个markdown文件到知识库
  gen_klg index  -ff batch_input.txt # 批量索引, 文件内部是按行存放到markdown文件列表
  gen_klg query  -q "tos如何上传文件" # 查询知识库
  gen_klg recall -f query.txt # 批量查询，文件内容用------分割
`)
}
