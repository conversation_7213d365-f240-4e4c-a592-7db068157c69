package main

import (
	"strings"

	"code.byted.org/stone/caribou/modules/agents/gen_klg_agent"
	"code.byted.org/stone/caribou_splitter"
)

type Chunk struct {
	ID       uint64
	Content  string
	Metadata map[string]any
}

func ChunkMarkdownOverlap(markdownContent string) ([]Chunk, error) {
	index := strings.Index(markdownContent, "#")
	if index >= 0 {
		markdownContent = markdownContent[index:]
	}
	capacity := 1000
	overlap := 100
	config := caribou_splitter.NewChunkConfig(caribou_splitter.NewChunkCapacity(capacity))
	var err error
	config, err = config.WithOverlap(overlap)
	if err != nil {
		return nil, err
	}
	splitter, err := caribou_splitter.NewSplitter(caribou_splitter.MarkdownSplitterType, config)
	if err != nil {
		return nil, err
	}
	chunks := splitter.Chunks(markdownContent)
	var result []Chunk
	for _, chunk := range chunks {
		_, signU64 := gen_klg_agent.MakeSign(chunk)
		result = append(result, Chunk{
			ID:       signU64,
			Content:  chunk,
			Metadata: map[string]any{},
		})
	}
	return result, nil
}

// func ChunkMarkdownByLevel(markdownContent string) ([]Chunk, error) {
// 	scanner := bufio.NewScanner(strings.NewReader(markdownContent))
// 	titleStack := []string{}
// 	var lastContent []string
// 	var lastTitleStack []string
// 	var chunks []Chunk
//
// 	// 标题正则
// 	re := regexp.MustCompile(`^(#{1,6})\s*(.+)$`)
//
// 	for scanner.Scan() {
// 		line := scanner.Text()
// 		if matches := re.FindStringSubmatch(line); matches != nil {
// 			// 新标题，先处理上一个分片
// 			if len(lastContent) > 0 && len(lastTitleStack) > 0 {
// 				var signList []uint64
// 				for _, t := range lastTitleStack {
// 					_, signU64 := gen_klg_agent.MakeSign(t)
// 					signList = append(signList, signU64)
// 				}
// 				var id uint64
// 				if len(signList) > 0 {
// 					id = signList[len(signList)-1]
// 				}
// 				chunks = append(chunks, Chunk{
// 					ID:      id,
// 					Content: strings.TrimSpace(strings.Join(lastContent, "\n")),
// 					Metadata: map[string]any{
// 						"title":     strings.Join(lastTitleStack, "/"),
// 						"sign_list": signList,
// 					},
// 				})
// 			}
// 			// 处理标题栈
// 			level := len(matches[1])
// 			title := matches[2]
// 			if level > len(titleStack) {
// 				// 新子级
// 				titleStack = append(titleStack, title)
// 			} else {
// 				// 回退到当前级别
// 				titleStack = titleStack[:level-1]
// 				titleStack = append(titleStack, title)
// 			}
// 			lastContent = []string{}
// 			lastTitleStack = append([]string{}, titleStack...)
// 		} else {
// 			// 正文内容
// 			if len(titleStack) > 0 {
// 				lastContent = append(lastContent, line)
// 			}
// 		}
// 	}
// 	// 文件结尾处理最后一个分片
// 	if len(lastContent) > 0 && len(lastTitleStack) > 0 {
// 		var signList []uint64
// 		for _, t := range lastTitleStack {
// 			_, signU64 := gen_klg_agent.MakeSign(t)
// 			signList = append(signList, signU64)
// 		}
// 		chunks = append(chunks, Chunk{
// 			ID:      signList[len(signList)-1],
// 			Content: strings.TrimSpace(strings.Join(lastContent, "\n")),
// 			Metadata: map[string]any{
// 				"title":     strings.Join(lastTitleStack, "/"),
// 				"sign_list": signList,
// 			},
// 		})
// 	}
// 	return chunks, nil
// }
