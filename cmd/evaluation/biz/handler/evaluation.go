package handler

import (
	"context"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path"

	"github.com/bytedance/sonic"
	"github.com/pkg/errors"

	"code.byted.org/kite/kitutil"
	"code.byted.org/middleware/hertz/pkg/app"
	herz_util "code.byted.org/middleware/hertz/pkg/common/utils"
	"code.byted.org/stone/caribou/cmd/evaluation/biz/docker"
	"code.byted.org/stone/caribou/pkg/tos"
	"code.byted.org/stone/caribou/utils/log_util"
)

const (
	image  = "hub.byted.org/stone/caribou.runtime.dev:0.0.3"
	tosURL = "evaluation/workflow/v1/%s"
)

var dm *docker.DockerManager

func CheckEvaluation(ctx context.Context, c *app.RequestContext) {
	defer log_util.Flush()

	id := c.Query("execution_detail_id")
	if len(id) <= 0 {
		log_util.CtxError(ctx, "method=(*EvaluationRequest).parse failed, err=invalid params")
		c.JSO<PERSON>(400, herz_util.H{
			"error": "无法解析请求, 缺少必要参数: execution_detail_id 是必需的",
		})
		return
	}
	obj, err := tos.GetClient().GetObject(ctx, fmt.Sprintf(tosURL, id))
	if err != nil {
		log_util.CtxError(ctx, "method=(*tos.Client).GetObject failed, err=%+v", err)
		c.JSON(200, herz_util.H{
			"session_status": "processing",
		})
		return
	}
	if string(obj) == "failed" {
		c.JSON(200, herz_util.H{
			"session_status": "failed",
		})
	} else {
		var data any
		err = sonic.Unmarshal(obj, &data)
		if err != nil {
			log_util.CtxError(ctx, "method=sonic.Unmarshal failed, err=%+v", err)
			c.JSON(500, herz_util.H{
				"session_status": "failed",
				"error":          "执行失败: " + err.Error(),
			})
			return
		}
		c.JSON(200, herz_util.H{
			"edited_files":   data,
			"session_status": "success",
		})
	}
}

func EvaluationCaribou(ctx context.Context, c *app.RequestContext) {
	var err error
	defer log_util.Flush()
	log_util.CtxInfo(ctx, "method=EvaluationCaribou start")
	defer log_util.CtxInfo(ctx, "method=EvaluationCaribou finish")
	defer log_util.Flush()

	req := &EvaluationRequest{}
	err = req.parse(c)
	if err != nil {
		log_util.CtxError(ctx, "method=(*EvaluationRequest).parse failed, err=%+v", err)
		c.JSON(400, herz_util.H{
			"session_status": "failed",
			"error":          "无法解析请求: " + err.Error(),
		})
		return
	}
	if req.DatasetMeta == nil || len(req.DatasetMeta.Query) <= 0 || len(req.DatasetMeta.TosURL) <= 0 {
		log_util.CtxError(ctx, "invalid params")
		c.JSON(400, herz_util.H{
			"session_status": "failed",
			"error":          "缺少必要参数: query, tos_url 都是必需的",
		})
		return
	}
	tos.GetClient().DelObject(ctx, fmt.Sprintf(tosURL, req.ID))
	defer func() {
		if err != nil {
			tos.GetClient().PutStringObject(ctx, fmt.Sprintf(tosURL, req.ID), "failed")
		}
	}()

	// 创建管道用于 fd3
	r, w, err := os.Pipe()
	if err != nil {
		c.JSON(500, herz_util.H{
			"session_status": "failed",
			"error":          "执行失败: " + err.Error(),
		})
		return
	}
	defer r.Close()
	defer w.Close()
	// 启动命令
	logID, _ := kitutil.GetCtxLogID(ctx)
	cmd := exec.Command(path.Join(docker.GetMountSource(ctx), "caribou-linux"),
		"workflow", "first", "--input", req.DatasetMeta.Query, "--tos-url", req.DatasetMeta.TosURL, "--logid", logID)
	cmd.ExtraFiles = []*os.File{w}
	err = cmd.Start()
	if err != nil {
		log_util.CtxError(ctx, "method=(*exec.Cmd).Start failed, err=%+v", err)
		c.JSON(500, herz_util.H{
			"session_status": "failed",
			"error":          "执行失败: " + err.Error(),
		})
		return
	}
	err = cmd.Wait()
	if err != nil {
		log_util.CtxError(ctx, "method=(*exec.Cmd).Wait failed, err=%+v", err)
		c.JSON(500, herz_util.H{
			"session_status": "failed",
			"error":          "执行失败: " + err.Error(),
		})
		return
	}
	w.Close()
	log_util.CtxInfo(ctx, "start read fd3")
	resp, err := io.ReadAll(r)
	if err != nil {
		log_util.CtxError(ctx, "method=io.ReadAll failed, err=%+v", err)
		c.JSON(500, herz_util.H{
			"session_status": "failed",
			"error":          "执行失败: " + err.Error(),
		})
		return
	}
	if string(resp) == "failed" {
		c.JSON(200, herz_util.H{
			"session_status": "failed",
		})
		log_util.CtxError(ctx, "cmd failed")
		err = errors.New("failed")
		return
	} else {
		var data any
		err = sonic.Unmarshal(resp, &data)
		if err != nil {
			log_util.CtxError(ctx, "method=sonic.Unmarshal failed, err=%+v", err)
			c.JSON(500, herz_util.H{
				"session_status": "failed",
				"error":          "执行失败: " + err.Error(),
			})
			return
		}
		c.JSON(200, herz_util.H{
			"session_status": "success",
			"edited_files":   data,
		})
	}
	// 存abase
	err = tos.GetClient().PutByteObject(ctx, fmt.Sprintf(tosURL, req.ID), resp)
	if err != nil {
		log_util.CtxError(ctx, "method=(*tos.Client).PutByteObject failed, err=%+v", err)
		c.JSON(500, herz_util.H{
			"session_status": "failed",
			"error":          "执行失败: " + err.Error(),
		})
		return
	}
}

// EvaluationCaribouInDocker 在Docker容器中处理FirstWorkflow请求
func EvaluationCaribouInDocker(ctx context.Context, c *app.RequestContext) {
	var (
		containerID string
		err         error
	)
	defer func() {
		if len(containerID) > 0 {
			dm.StopContainer(ctx, containerID)
			dm.CleanupContainer(ctx, containerID)
		}
		log_util.Flush()
	}()
	req := &EvaluationRequest{}
	err = req.parse(c)
	if err != nil {
		log_util.CtxError(ctx, "method=(*EvaluationRequest).parse failed, err=%+v", err)
		c.JSON(400, herz_util.H{
			"error": "无法解析请求: " + err.Error(),
		})
		return
	}
	if req.DatasetMeta == nil || len(req.DatasetMeta.Query) <= 0 || len(req.DatasetMeta.TosURL) <= 0 {
		log_util.CtxError(ctx, "invalid params")
		c.JSON(400, herz_util.H{
			"error": "缺少必要参数: query, tos_url 都是必需的",
		})
		return
	}

	containerID, err = dm.StartContainer(ctx, image, 8080)
	if err != nil {
		log_util.CtxError(ctx, "method=(*DockerManager).StartContainer failed, err=%+v", err)
		c.JSON(500, herz_util.H{
			"error": "启动容器失败: " + err.Error(),
		})
		return
	} // 在Docker容器中执行FirstWorkflow命令
	resp, err := dm.ExecInContainer(context.Background(), containerID, docker.MountTarget+"/caribou-linux",
		[]string{"workflow", "first", "--input", req.DatasetMeta.Query, "--tos-url", req.DatasetMeta.TosURL}, "", []string{})
	if err != nil {
		log_util.CtxError(ctx, "method=(*DockerManager).ExecInContainer failed, err=%+v", err)
		c.JSON(500, herz_util.H{
			"error": "执行失败: " + err.Error(),
		})
		return
	}
	// 存abase
	err = tos.GetClient().PutStringObject(ctx, fmt.Sprintf(tosURL, req.ID), resp.Output)
	if err != nil {
		log_util.CtxError(ctx, "method=(*tos.Client).PutStringObject failed, err=%+v", err)
	}
	c.JSON(200, herz_util.H{
		"edited_files":   resp.Output,
		"session_status": "success",
	})
}

type EvaluationRequest struct {
	ID          string       `json:"execution_detail_id"`
	DatasetMeta *DatesetMeta `json:"dataset_meta"`
}

type DatesetMeta struct {
	Query  string `json:"query"`
	TosURL string `json:"repo_tos_url"`
}

func (r *EvaluationRequest) parse(c *app.RequestContext) error {
	bytes, err := c.Body()
	if err != nil {
		return errors.Wrapf(err, "method=(*app.RequestContext).Body failed")
	}
	err = sonic.Unmarshal(bytes, r)
	if err != nil {
		return errors.Wrapf(err, "method=sonic.Unmarshal failed")
	}

	return nil
}
