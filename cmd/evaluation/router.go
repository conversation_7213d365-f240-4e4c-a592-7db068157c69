package main

import (
	"code.byted.org/middleware/hertz/pkg/app/server"
	"code.byted.org/stone/caribou/cmd/evaluation/biz/handler"
)

// customizeRegister register customize routers.
func customizeRegister(r *server.Hertz) {
	// your code ...
	r.GET("/", handler.HelloWorld)

	// NOTE: GET v1/ping should be provided for liveness check by FaaS Platform.
	// Status Code OK denotes that the service is healthy.
	r.GET("/v1/ping", handler.Ping)

	r.POST("/v1/evaluation/workflow", handler.EvaluationCaribou)

	r.GET("/v1/evaluation/workflow_check", handler.CheckEvaluation)
}
