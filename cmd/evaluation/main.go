package main

import (
	"os"
	"strconv"
	"time"

	"code.byted.org/middleware/hertz/byted"
	"code.byted.org/middleware/hertz/pkg/app/server"
	"code.byted.org/middleware/hertz/pkg/common/config"
	"code.byted.org/stone/caribou/cmd/evaluation/biz/handler"
	"code.byted.org/stone/caribou/cmd/evaluation/conf"
	"code.byted.org/stone/caribou/pkg/tos"
)

func main() {
	byted.Init()
	mustInit()

	opts := []config.Option{}
	opts = append(opts, server.WithExitWaitTime(GetGracefulTime()))

	r := byted.Default(opts...)

	register(r)

	r.Spin()
}

func GetGracefulTime() time.Duration {
	gracefulTimeout, err := strconv.Atoi(os.Getenv("_BYTEFAAS_FUNC_TIMEOUT"))
	if err != nil {
		gracefulTimeout = 30
	}
	return time.Duration(gracefulTimeout) * time.Second
}

func mustInit() {
	conf.MustInit()
	tos.MustInit(&tos.TosConfig{
		BucketName: conf.GetTosConfig().BucketName,
		AccessKey:  conf.GetTosConfig().AccessKey,
	})
	handler.MustInit()
}
