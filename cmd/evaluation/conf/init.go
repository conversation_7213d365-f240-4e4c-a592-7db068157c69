package conf

import (
	"embed"
	"gopkg.in/yaml.v3"

	"code.byted.org/gopkg/env"
)

//go:embed settings/*
var staticFiles embed.FS

func MustInit() {
	mustInitTosConfig()
}

func mustInitTosConfig() {
	data, err := staticFiles.ReadFile("settings/tos_conf.yaml")
	if err != nil {
		panic(err)
	}
	tosConfig = &tosConf{}
	err = yaml.Unmarshal(data, &tosConfig)
	if err != nil {
		panic(err)
	}
}

func isBoe() bool {
	return env.IsBoe()
}

func isBoeCN() bool {
	return env.IsBoeCN()
}

func isBoeI18N() bool {
	return env.IsBoeI18N()
}

func isCN() bool {
	return env.Region() == env.R_CN
}

func isPPE() bool {
	return env.IsPPE()
}
