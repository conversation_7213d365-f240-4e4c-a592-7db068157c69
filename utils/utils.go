package utils

import (
	"context"
	"crypto/sha256"
	"encoding/hex"

	"github.com/bytedance/sonic"
	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"

	"code.byted.org/stone/caribou/utils/log_util"
)

func Jsonify(data interface{}) string {
	dump, err := sonic.MarshalString(data)
	if err != nil {
		log_util.CtxError(context.Background(), "sonic.MarshalString err: %v", err)
	}

	return dump
}

func Hash256(data string) string {
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}

var validate = validator.New()

func Validate(s interface{}) error {
	err := validate.Struct(s)
	if err != nil {
		return errors.Wrapf(err, "method=(*Validate).Struct failed")
	}

	return nil
}
