package file_util

type FileSystem interface {

	// WriteFile 写入文件
	WriteFile(path string, content []byte) error

	// ReadFile 读取文件
	ReadFile(path string) ([]byte, error)

	ReadFileLines(path string) ([]string, error)

	ReplaceContent(params *ReplaceContentParams) (bool, error)

	// Exists 检查文件是否存在
	Exists(path string) bool

	// MkdirAll 创建目录
	MkdirAll(path string) error

	// ReadDir 列出目录内容
	ReadDir(path string) ([]DirEntry, error)

	// Remove 删除文件或目录
	Remove(path string) error

	// CopyFile 复制文件
	CopyFile(src, dst string) error
}

type DirEntry interface {
	Name() string

	IsDir() bool
}

type ReplaceContentParams struct {
	FilePath  string
	StartLine int
	EndLine   int
	OldString string
	NewString string
}
