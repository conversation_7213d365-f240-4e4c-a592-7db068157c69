package file_util

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"path/filepath"

	"github.com/go-git/go-billy/v5"
	"github.com/pkg/errors"
)

func NewFileSystem(fs billy.Filesystem, isMemory bool, baseDir string) FileSystem {
	return &FileSystemImpl{
		fs:       fs,
		isMemory: isMemory,
		baseDir:  baseDir,
	}
}

type FileSystemImpl struct {
	fs       billy.Filesystem
	isMemory bool
	baseDir  string
}

// WriteFile 写入文件
func (fs *FileSystemImpl) WriteFile(path string, content []byte) error {
	if fs.isMemory {
		file, err := fs.fs.Create(path)
		if err != nil {
			return errors.Wrapf(err, "method=Create failed, path=%s", path)
		}
		defer file.Close()
		_, err = file.Write(content)
		return err
	}
	fullPath := filepath.Join(fs.baseDir, path)

	return os.WriteFile(fullPath, content, 0644)
}

// ReadFile 读取文件
func (fs *FileSystemImpl) ReadFile(path string) ([]byte, error) {
	if fs.isMemory {
		file, err := fs.fs.Open(path)
		if err != nil {
			return nil, errors.Wrapf(err, "method=FileSystem.Open failed, path=%s", path)
		}
		defer file.Close()
		return io.ReadAll(file)
	}
	fullPath := filepath.Join(fs.baseDir, path)

	return os.ReadFile(fullPath)
}

func (fs *FileSystemImpl) ReadFileLines(path string) (lines []string, err error) {
	var reader io.Reader
	if fs.isMemory {
		file, err := fs.fs.Open(path)
		if err != nil {
			return nil, errors.Wrapf(err, "method=FileSystem.Open failed, path=%s", path)
		}
		defer file.Close()
		reader = file
	} else {
		fullPath := filepath.Join(fs.baseDir, path)
		file, err := os.Open(fullPath)
		if err != nil {
			return nil, errors.Wrapf(err, "method=os.Open failed, path=%s", path)
		}
		defer file.Close()
		reader = file
	}
	scanner := bufio.NewScanner(reader)
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}
	if err := scanner.Err(); err != nil {
		return nil, errors.Wrapf(err, "method=Scanner.Scan failed")
	}

	return lines, nil
}

func (fs *FileSystemImpl) ReplaceContent(params *ReplaceContentParams) (bool, error) {
	if len(params.FilePath) <= 0 {
		return false, errors.New("no file path")
	}
	if !fs.Exists(params.FilePath) {
		return false, errors.New(fmt.Sprintf("file not found, path=%s", params.FilePath))
	}

	lines, err := fs.ReadFileLines(params.FilePath)
	if err != nil {
		return false, errors.Wrapf(err, "method=ReadFileLines failed")
	}
	if len(lines) <= 0 {
		return true, nil
	}
	// 修正和验证行号
	startLine, endLine, err := normalizeLineNumbers(params.StartLine, params.EndLine, len(lines))
	if err != nil {
		return false, errors.Wrapf(err, "method=normalizeLineNumbers failed")
	}
	// 尝试多种策略进行替换
	newLines, replaced, err := performReplacement(lines, startLine, endLine, params.OldString, params.NewString)
	if err != nil {
		return false, errors.Wrapf(err, "method=performReplacement failed")
	}
	if !replaced {
		return false, nil
	}
	// 写回文件
	var newFileContent string
	for i, line := range newLines {
		if i > 0 || i < len(lines)-1 {
			newFileContent += "\n"
		}
		newFileContent += line
	}
	err = fs.WriteFile(params.FilePath, []byte(newFileContent))
	if err != nil {
		return false, errors.Wrapf(err, "method=WriteFile failed")
	}

	return true, nil
}

// Exists 检查文件是否存在
func (fs *FileSystemImpl) Exists(path string) bool {
	if fs.isMemory {
		_, err := fs.fs.Stat(path)
		return err == nil
	}
	fullPath := filepath.Join(fs.baseDir, path)
	_, err := os.Stat(fullPath)

	return err == nil
}

// MkdirAll 创建目录
func (fs *FileSystemImpl) MkdirAll(path string) error {
	if fs.isMemory {
		return fs.fs.MkdirAll(path, 0755)
	}
	fullPath := filepath.Join(fs.baseDir, path)

	return os.MkdirAll(fullPath, 0755)
}

// ReadDir 列出目录内容
func (fs *FileSystemImpl) ReadDir(path string) ([]DirEntry, error) {
	if fs.isMemory {
		files, err := fs.fs.ReadDir(path)
		if err != nil {
			return nil, errors.Wrapf(err, "method=FileSystem.ReadDir failed, path=%s", path)
		}
		var dirEntries []DirEntry
		for _, f := range files {
			dirEntries = append(dirEntries, f)
		}
		return dirEntries, nil
	}

	fullPath := filepath.Join(fs.baseDir, path)
	entries, err := os.ReadDir(fullPath)
	if err != nil {
		return nil, errors.Wrapf(err, "method=os.ReadDir failed, path=%s", fullPath)
	}
	var dirEntries []DirEntry
	for _, entry := range entries {
		dirEntries = append(dirEntries, entry)
	}

	return dirEntries, nil
}

// Remove 删除文件或目录
func (fs *FileSystemImpl) Remove(path string) error {
	if fs.isMemory {
		return fs.fs.Remove(path)
	}
	fullPath := filepath.Join(fs.baseDir, path)

	return os.Remove(fullPath)
}

func (fs *FileSystemImpl) CopyFile(src, dst string) error {
	// 读取源文件内容
	srcFile, err := fs.ReadFile(src)
	if err != nil {
		return errors.Wrapf(err, "method=FileSystem.ReadFile failed, src=%s", src)
	}
	// 写入目标文件
	return fs.WriteFile(dst, srcFile)
}

// diskFilesystem 是一个磁盘上文件系统的简单实现
type diskFilesystem struct {
	baseDir string
}

func (fs *diskFilesystem) Create(filename string) (billy.File, error) {
	fullPath := filepath.Join(fs.baseDir, filename)
	dir := filepath.Dir(fullPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, errors.Wrapf(err, "method=os.MkdirAll failed, dir=%s", dir)
	}
	file, err := os.Create(fullPath)
	if err != nil {
		return nil, errors.Wrapf(err, "method=os.Create failed, filename=%s", filename)
	}

	return &diskFile{file}, nil
}

func (fs *diskFilesystem) Open(filename string) (billy.File, error) {
	fullPath := filepath.Join(fs.baseDir, filename)
	file, err := os.Open(fullPath)
	if err != nil {
		return nil, errors.Wrapf(err, "method=os.Open failed, filename=%s", filename)
	}

	return &diskFile{file}, nil
}

func (fs *diskFilesystem) OpenFile(filename string, flag int, perm os.FileMode) (billy.File, error) {
	fullPath := filepath.Join(fs.baseDir, filename)
	dir := filepath.Dir(fullPath)
	if flag&os.O_CREATE != 0 {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return nil, errors.Wrapf(err, "method=os.MkdirAll failed, dir=%s", dir)
		}
	}
	file, err := os.OpenFile(fullPath, flag, perm)
	if err != nil {
		return nil, errors.Wrapf(err, "method=os.OpenFile failed, filename=%s", filename)
	}

	return &diskFile{file}, nil
}

func (fs *diskFilesystem) Stat(filename string) (os.FileInfo, error) {
	fullPath := filepath.Join(fs.baseDir, filename)

	return os.Stat(fullPath)
}

func (fs *diskFilesystem) Rename(oldpath, newpath string) error {
	fullOldPath := filepath.Join(fs.baseDir, oldpath)
	fullNewPath := filepath.Join(fs.baseDir, newpath)

	return os.Rename(fullOldPath, fullNewPath)
}

func (fs *diskFilesystem) Remove(filename string) error {
	fullPath := filepath.Join(fs.baseDir, filename)

	return os.Remove(fullPath)
}

func (fs *diskFilesystem) Join(elem ...string) string {
	return filepath.Join(elem...)
}

func (fs *diskFilesystem) TempFile(dir, prefix string) (billy.File, error) {
	fullDir := filepath.Join(fs.baseDir, dir)
	if err := os.MkdirAll(fullDir, 0755); err != nil {
		return nil, err
	}
	file, err := os.CreateTemp(fullDir, prefix)
	if err != nil {
		return nil, err
	}

	return &diskFile{file}, nil
}

func (fs *diskFilesystem) ReadDir(path string) ([]os.FileInfo, error) {
	fullPath := filepath.Join(fs.baseDir, path)
	entries, err := os.ReadDir(fullPath)
	if err != nil {
		return nil, err
	}
	var infos []os.FileInfo
	for _, entry := range entries {
		info, err := entry.Info()
		if err != nil {
			return nil, err
		}
		infos = append(infos, info)
	}

	return infos, nil
}

func (fs *diskFilesystem) MkdirAll(path string, perm os.FileMode) error {
	fullPath := filepath.Join(fs.baseDir, path)
	return os.MkdirAll(fullPath, perm)
}

func (fs *diskFilesystem) Lstat(filename string) (os.FileInfo, error) {
	fullPath := filepath.Join(fs.baseDir, filename)
	return os.Lstat(fullPath)
}

func (fs *diskFilesystem) Symlink(target, link string) error {
	fullLink := filepath.Join(fs.baseDir, link)
	return os.Symlink(target, fullLink)
}

func (fs *diskFilesystem) Readlink(link string) (string, error) {
	fullLink := filepath.Join(fs.baseDir, link)
	return os.Readlink(fullLink)
}

func (fs *diskFilesystem) Chroot(path string) (billy.Filesystem, error) {
	fullPath := filepath.Join(fs.baseDir, path)
	return &diskFilesystem{baseDir: fullPath}, nil
}

func (fs *diskFilesystem) Root() string {
	return fs.baseDir
}

// Capabilities 返回文件系统的能力
func (fs *diskFilesystem) Capabilities() billy.Capability {
	return billy.AllCapabilities
}

// diskFile 是一个包装了os.File的billy.File实现
type diskFile struct {
	*os.File
}

func (f *diskFile) Lock() error {
	return nil // 不实现锁
}

func (f *diskFile) Unlock() error {
	return nil // 不实现锁
}
