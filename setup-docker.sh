#!/bin/bash

# 设置 - 获取 Mac 上的 Go 路径
HOST_GOPATH=$(go env GOPATH)
CONTAINER_NAME="caribou-dev"
GO_VERSION="0.0.3"

# 检查 Go 是否已安装在 Mac 上
if [ -z "$HOST_GOPATH" ]; then
  echo "错误: 无法获取 GOPATH。请确保 Go 已安装在您的 Mac 上。"
  exit 1
fi

echo "使用 Mac 上的 GOPATH: $HOST_GOPATH"

# 停止并移除之前的容器（如果存在）
if docker ps -a | grep -q $CONTAINER_NAME; then
  docker stop $CONTAINER_NAME > /dev/null 2>&1
  docker rm $CONTAINER_NAME > /dev/null 2>&1
fi

# 获取script所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# 编译 caribou 二进制文件
echo "编译 caribou 二进制文件..."
sh $SCRIPT_DIR/install.sh > /dev/null
# 定义caribou参数
AgentCMD="workflow first --input 你好 --base-branch main --repo-url https://code.byted.org/stone/caribou.git --repo-token wZWybwsggTLjHWi2fgsi"

# 启动新的 Docker 容器
echo "启动新的 hub.byted.org/stone/caribou.runtime.dev:$GO_VERSION 容器并挂载 Go mod 缓存..."
echo "执行 caribou..."

docker run -it --name $CONTAINER_NAME \
  -v "$HOST_GOPATH/pkg/mod:/go/pkg/mod" \
  -v "$(pwd):/workspace" \
  -w /workspace \
  -e CONSUL_HTTP_HOST=common-consul-boe.bytedance.net -e RUNTIME_IDC_NAME=boe -e METRICS_LOG_LEVEL=5 -e test_env=SEC_TOKEN_STRING \
  hub.byted.org/stone/caribou.runtime.dev:$GO_VERSION /workspace/bin/caribou $AgentCMD