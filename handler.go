package codedancer_agent

import (
	"context"
	"fmt"
	"strings"

	"code.byted.org/gopkg/env"
	"code.byted.org/stone/codedancer_agent/domain/shark"
	"code.byted.org/stone/codedancer_agent/utils/goroutine_util"

	"github.com/cloudwego/kitex/pkg/rpcinfo"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/stone/codedancer_agent/conf"
	"code.byted.org/stone/codedancer_agent/domain/chat"
	"code.byted.org/stone/codedancer_agent/domain/file_process"
	"code.byted.org/stone/codedancer_agent/domain/module_agent/plan_react"
	"code.byted.org/stone/codedancer_agent/domain/multi_agent"
	"code.byted.org/stone/codedancer_agent/domain/pack_service"
	"code.byted.org/stone/codedancer_agent/domain/project"
	"code.byted.org/stone/codedancer_agent/kitex_gen/codedancer/core_service/coding_agent"
	"code.byted.org/stone/codedancer_agent/kitex_gen/codedancer/core_service/coding_agent/common"
	"code.byted.org/stone/codedancer_agent/kitex_gen/codedancer/core_service/coding_agent/platform"
	"code.byted.org/stone/codedancer_agent/model"
	"code.byted.org/stone/codedancer_agent/pkg/cqc"
	"code.byted.org/stone/codedancer_agent/pkg/frontier"
	"code.byted.org/stone/codedancer_agent/pkg/mysql"
	"code.byted.org/stone/codedancer_agent/pkg/tcc"
	"code.byted.org/stone/codedancer_agent/pkg/tns"
	"code.byted.org/stone/codedancer_agent/utils"
	"code.byted.org/stone/codedancer_agent/utils/errorx"
	"code.byted.org/stone/codedancer_agent/utils/file_util"
	"code.byted.org/stone/codedancer_agent/utils/message_util"
)

func errLogTraffic(ctx context.Context, req interface{}, err *error) {
	if err == nil || *err == nil {
		return
	}
	logs.CtxError(ctx, "method=%s, req=%v, err=%+v", rpcinfo.GetRPCInfo(ctx).To().Method(), utils.Jsonify(req), *err)
}

var _ coding_agent.CodingAgentService = &CodingAgentServiceImpl{}

// CodingAgentServiceImpl implements the last service interface defined in the IDL.
type CodingAgentServiceImpl struct{}

func (s *CodingAgentServiceImpl) Chat(ctx context.Context, req *platform.ChatReq) (r *platform.ChatResp, err error) {
	r = platform.NewChatResp()
	if req == nil || message_util.IsEmptyMessage(req.Message) {
		return r, errorx.InvalidParamError
	}

	//get userID
	projectDO, err := project.GetProjectService().GetByID(mysql.DB.UseWriteDB(ctx), req.ProjectID)
	if err != nil {
		return r, errorx.Wrapf(err, "method=ProjectService.GetByID failed")
	}

	// shark security check
	if conf.GetSharkConfig().NeedShark == 1 && env.Region() != env.R_CN {
		userID := projectDO.UserID
		userInput := chat.GetMessageParser().ParseMessageForCoding(chat.ConvertMessageDTO2DO(req.Message)).GetContent()

		sharkCheck, err := shark.GetSharkCheckService().GetSharkCheckResult(ctx, &shark.GetSharkCheckParams{
			UserID:  userID,
			AppID:   conf.GetSharkConfig().AppID,
			AppName: conf.GetSharkConfig().AppName,
			ChatStr: userInput,
		})

		if shark.SecureFinalDecisionPass != sharkCheck {
			//shark block
			return r, errorx.Wrapf(err, "method=SharkCheckService.GetSharkCheckResult check no pass: %+v", err)
		}
	}

	// cqc check
	cqcClient := cqc.GetClient()
	cqcCheckResult, err := cqcClient.CheckUserQuery(ctx, cqc.NewCheckUserQueryParams(req))
	if err != nil {
		return r, errorx.InvalidParamError
	}
	if !cqcCheckResult {
		pack_service.GetCozeTaskService().UpdateCozeTask(ctx, &pack_service.UpdateCozeTaskParams{
			ProjectID: req.ProjectID,
			UserID:    projectDO.UserID,
			// TaskStop = 5 // 终止
			TaskStatus: 5,
		})
		return r, errorx.CqcCheckQueryError
	}

	var session chat.ChatSession
	err = mysql.DB.Transaction(ctx, func(txCtx context.Context) error {
		hasSession, err := chat.GetChatCombinationService().HasActiveSession(ctx, req.ProjectID)
		if err != nil {
			return err
		}
		if hasSession {
			return errorx.ChatDuplicateRequestError
		}
		session, err = chat.GetChatCombinationService().CreateChatSession(ctx, &chat.CreateChatSessionParams{
			Project:         projectDO,
			UserMessageType: req.GetType(),
			UserMessage:     chat.ConvertMessageDTO2DO(req.Message),
			TaskConfig: &model.ChatTaskConfig{
				ChatStage:     multi_agent.GetCodingAgentManager().GetCurrentChatStage(ctx, projectDO),
				PushTargets:   chat.ConvertFrontierPushTargetsDTO2DO(req.PushConfig),
				RuntimeOption: multi_agent.ConvertChatOptions2ExecuteOptions(req.Options),
			},
		})
		if err != nil {
			return errorx.Wrapf(err, "method=ChatCombinationService.CreateChatSession failed")
		}
		r.MessageID = session.GetUserMessage().ID
		return nil
	})
	if err != nil {
		return r, err
	}
	// quota check
	hasAcquired := pack_service.GetQuotaControlService().AcquireSeat(ctx, &pack_service.AcquireSeatParams{
		ProjectID: req.ProjectID,
		UserID:    projectDO.UserID,
		Request: utils.Jsonify(&platform.ResumeChatReq{
			ProjectID: req.ProjectID,
			MessageID: session.GetUserMessage().ID,
			Base:      req.Base,
		}),
	})
	r.HasAcquiredSeat = conv.BoolPtr(hasAcquired)
	if !hasAcquired {
		return r, nil
	}
	_, err = chat.GetChatHistoryService().UpdateChatStatus(ctx, &chat.UpdateChatStatusParams{
		ProjectID:      req.ProjectID,
		ChatMessageIDs: []int64{session.GetUserMessage().ID, session.GetAssistantMessage().ID},
		FromChatStatus: common.ChatMessageStatusPtr(common.ChatMessageStatus_Pending),
		ChatStatus:     common.ChatMessageStatus_Processing,
	})
	if err != nil {
		logs.CtxError(ctx, "[CodingAgentServiceImpl.Chat] method=ChatHistoryService.UpdateChatStatus failed, err=%+v", err)
	}

	if projectDO.Phase == common.ProjectPhase_Creation || projectDO.Phase == common.ProjectPhase_Requirement {
		err = pack_service.GetCozeTaskService().UpdateCozeTask(ctx, &pack_service.UpdateCozeTaskParams{
			ProjectID: req.ProjectID,
			UserID:    projectDO.UserID,
			// Running
			TaskStatus: 1,
		})
		if err != nil {
			logs.CtxError(ctx, "[CodingAgentServiceImpl.Chat] method=PackService.UpdateTaskStatus failed, err=%+v", err)
		}
	}

	executableTask, err := session.GetNextExecutablePendingTask(ctx)
	if err != nil {
		return r, err
	}
	err = multi_agent.GetCodingAgentManager().ExecuteChat(ctx, &multi_agent.ExecuteParams{
		AgentVersion:     projectDO.ConvertMode2CodingAgentVersion(),
		Project:          projectDO,
		ChatTask:         executableTask,
		UserMessage:      session.GetUserMessage(),
		AssistantMessage: session.GetAssistantMessage(),
		PushTargets:      chat.ConvertFrontierPushTargetsDTO2DO(req.PushConfig),
		Options:          multi_agent.ConvertChatOptions2ExecuteOptions(req.Options),
	})
	if err != nil {
		return r, err
	}

	return r, nil
}

func (s *CodingAgentServiceImpl) ChatRetry(ctx context.Context, req *platform.ChatRetryReq) (r *platform.ChatRetryResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	r = platform.NewChatRetryResp()

	projectDO, err := project.GetProjectService().GetByID(ctx, req.ProjectID)
	if err != nil {
		return r, err
	}
	chatMessages, err := chat.GetChatHistoryService().MGetChatMessages(ctx, &chat.MGetChatMessagesParams{
		ProjectID:        req.ProjectID,
		ParentID:         conv.Int64Ptr(0),
		Limit:            2,
		ChatMessageTypes: []common.ChatMessageType{common.ChatMessageType_Conversation, common.ChatMessageType_Autofix},
		IsAscend:         false,
	})
	if err != nil {
		return r, err
	}
	// 只能retry最后一条user message，且这条消息的状态必须为Failure
	if len(chatMessages) < 2 || chatMessages[1].ID != req.MessageID || chatMessages[1].Status != common.ChatMessageStatus_Failure {
		return r, errorx.Wrapf(errorx.InvalidParamError, "invalid chat messages")
	}
	rowEffected, err := chat.GetChatHistoryService().UpdateChatStatus(ctx, &chat.UpdateChatStatusParams{
		ProjectID:      req.ProjectID,
		ChatMessageIDs: []int64{chatMessages[0].ID, chatMessages[1].ID},
		FromChatStatus: common.ChatMessageStatusPtr(common.ChatMessageStatus_Failure),
		ChatStatus:     common.ChatMessageStatus_Processing,
	})
	if err != nil {
		return r, nil
	}
	if rowEffected <= 0 {
		return r, nil
	}
	options := multi_agent.ConvertChatOptions2ExecuteOptions(req.Options)
	options.IsRetry = true
	chatTasks, err := chat.GetChatTaskService().MGetChatTask(ctx, &chat.MGetChatTaskParams{
		ProjectID:             req.ProjectID,
		UserMessageIDs:        []int64{chatMessages[1].ID},
		Statuses:              []common.ChatTaskStatus{common.ChatTaskStatus_Failure}, // todo 后续需要优化，failure之后要流转task的状态
		Limit:                 100,
		OrderAscByUpdatedTime: conv.BoolPtr(false),
	})
	if err != nil {
		return r, err
	}
	if len(chatTasks) <= 0 {
		return r, errorx.InvalidParamError
	}

	// 0-1阶段失败时回滚了phase，防止Chat失败，ChatRetry时需要恢复phase
	if projectDO != nil && projectDO.Phase < common.ProjectPhase_Enhancing && len(chatMessages) > 0 && chatMessages[0] != nil {
		chatLogList, _, err := chat.GetChatLogService().MGetChatLogMeta(ctx, &chat.MGetChatLogMetaParams{
			ProjectID:             req.ProjectID,
			AssistantChatID:       chatMessages[0].ID,
			OrderAscByCreatedTime: false,
			Limit:                 2,
		})
		if err != nil {
			return r, err
		}
		if len(chatLogList) > 0 && chatLogList[0].Status == common.ChatLogStatus_Failure {
			targetPhase := common.ProjectPhase_Requirement
			switch chatLogList[0].ChatType {
			case common.ChatStage_Planing:
				targetPhase = common.ProjectPhase_Plan
			case common.ChatStage_Coding, common.ChatStage_Linting, common.ChatStage_Building, common.ChatStage_Summary:
				targetPhase = common.ProjectPhase_Coding
			}

			err = project.GetProjectService().UpdatePhase(ctx, &project.UpdateProjectPhaseParams{
				ProjectID: req.GetProjectID(),
				Phase:     targetPhase,
			})
			if err != nil {
				return r, err
			}
		}
	}

	chatSession, err := chat.GetFailedChatSession(ctx, &chat.GetFailedChatSessionParams{
		Project:          projectDO,
		UserMessage:      chatMessages[1],
		AssistantMessage: chatMessages[0],
		Tasks:            chatTasks,
	})
	if err != nil {
		logs.CtxError(ctx, "[CodingAgentServiceImpl.ChatRetry] method=Chat.GetFailedChatSession failed, err=%+v", err)
		return r, err
	}
	err = chatSession.UpdateTaskStatus(ctx, common.ChatTaskStatus_Pending)
	if err != nil {
		return r, err
	}
	err = multi_agent.GetCodingAgentManager().ExecuteChat(ctx, &multi_agent.ExecuteParams{
		AgentVersion:     projectDO.ConvertMode2CodingAgentVersion(),
		Project:          projectDO,
		ChatTask:         chatSession.GetTask(),
		UserMessage:      chatMessages[1],
		AssistantMessage: chatMessages[0],
		PushTargets:      chat.ConvertFrontierPushTargetsDTO2DO(req.PushConfig),
		Options:          options,
	})
	if err != nil {
		return r, err
	}
	r.MessageID = chatMessages[1].ID

	return r, nil
}

func (s *CodingAgentServiceImpl) ResumeChat(ctx context.Context, req *platform.ResumeChatReq) (r *platform.ResumeChatResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	r = platform.NewResumeChatResp()

	projectDO, err := project.GetProjectService().GetByID(ctx, req.ProjectID)
	if err != nil {
		return r, err
	}
	chatMessages, err := chat.GetChatHistoryService().MGetChatMessages(ctx, &chat.MGetChatMessagesParams{
		ProjectID:        req.ProjectID,
		ParentID:         conv.Int64Ptr(0),
		Limit:            2,
		ChatMessageTypes: []common.ChatMessageType{common.ChatMessageType_Conversation, common.ChatMessageType_Autofix},
		IsAscend:         false,
	})
	if err != nil {
		return r, err
	}
	// 只能resume最后一对chat messages，且这条消息的状态必须为Pending
	if len(chatMessages) < 2 || chatMessages[1].Status != common.ChatMessageStatus_Pending {
		return r, errorx.InvalidParamError
	}
	rowEffected, err := chat.GetChatHistoryService().UpdateChatStatus(ctx, &chat.UpdateChatStatusParams{
		ProjectID:      req.ProjectID,
		ChatMessageIDs: []int64{chatMessages[0].ID, chatMessages[1].ID},
		FromChatStatus: common.ChatMessageStatusPtr(common.ChatMessageStatus_Pending),
		ChatStatus:     common.ChatMessageStatus_Processing,
	})
	if err != nil {
		return r, nil
	}
	if rowEffected <= 0 {
		return r, nil
	}
	chatTasks, err := chat.GetChatTaskService().MGetChatTask(ctx, &chat.MGetChatTaskParams{
		ProjectID:      req.ProjectID,
		UserMessageIDs: []int64{chatMessages[1].ID},
	})
	if err != nil {
		return r, err
	}
	if len(chatTasks) <= 0 {
		return r, errorx.InvalidParamError
	}
	options := chatTasks[0].Config.RuntimeOption
	err = multi_agent.GetCodingAgentManager().ExecuteChat(ctx, &multi_agent.ExecuteParams{
		AgentVersion:     projectDO.ConvertMode2CodingAgentVersion(),
		Project:          projectDO,
		ChatTask:         chatTasks[0],
		UserMessage:      chatMessages[1],
		AssistantMessage: chatMessages[0],
		PushTargets:      chatTasks[0].Config.PushTargets,
		Options:          options,
	})
	if err != nil {
		return r, err
	}

	return r, nil
}

func (s *CodingAgentServiceImpl) CancelChat(ctx context.Context, req *platform.CancelChatReq) (r *platform.CancelChatResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	r = platform.NewCancelChatResp()

	message, err := chat.GetChatHistoryService().GetChatMessageByID(ctx, req.MessageID, false)
	if err != nil {
		return r, err
	}
	if message.ProjectID != req.ProjectID {
		return r, errorx.InvalidParamError
	}
	chatStatus, err := chat.GetChatHistoryService().GetChatStatus(ctx, &chat.GetChatStatusParams{
		ProjectID:     req.ProjectID,
		ChatMessageID: req.MessageID,
	})
	if err != nil {
		return r, err
	}
	if chatStatus != common.ChatMessageStatus_Processing {
		return r, errorx.Wrapf(errorx.CancelChatFailedError, fmt.Sprintf("current status=%v", chatStatus))
	}
	// todo cancel加场景，区分是用户主动cancel还是shark风控cancel
	err = multi_agent.GetCodingAgentManager().CancelChat(ctx, &multi_agent.CancelChatParams{
		ProjectID:      req.ProjectID,
		ChatMessageIDs: []int64{req.MessageID},
	})
	if err != nil {
		return r, err
	}
	cancelMessage, err := chat.GetChatHistoryService().GetChatMessageByID(ctx, req.GetMessageID(), true)
	if err != nil {
		return r, err
	}
	r.CancelMessage = chat.ConvertMessageDO2DTO(cancelMessage.GetMessage())

	//get userID
	projectDO, err := project.GetProjectService().GetByID(mysql.DB.UseWriteDB(ctx), req.ProjectID)
	if err != nil {
		return r, errorx.Wrapf(err, "method=ProjectService.GetByID failed")
	}
	// TaskFinish = 3, // 一轮任务完成
	// TaskInterrupt = 6, // 等待用户交互
	taskStatus := int32(3)
	if projectDO.Phase != common.ProjectPhase_Enhancing {
		taskStatus = 6
	}
	pack_service.GetCozeTaskService().UpdateCozeTask(ctx, &pack_service.UpdateCozeTaskParams{
		ProjectID:  req.ProjectID,
		UserID:     projectDO.UserID,
		TaskStatus: taskStatus,
	})

	return r, nil
}

// MGetChatHistory 前端分页的limit size需要>=2
func (s *CodingAgentServiceImpl) MGetChatHistory(ctx context.Context, req *platform.MGetChatHistoryReq) (r *platform.MGetChatHistoryResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	r = platform.NewMGetChatHistoryResp()

	params := &model.MGetChatMessageForDisplayParams{
		ProjectID:       req.ProjectID,
		MessageIDCursor: req.GetMessageIDCursor(),
		Limit:           int32(req.Limit),
	}
	chatMessages, userMessageID2ChatTasks, hasMore, err := chat.GetChatHistoryService().MGetChatMessageForDisplay(ctx, params)
	if err != nil {
		return r, err
	}
	r.ChatMessages = chat.ConvertChatMessageDOs2DTOs(chatMessages)
	r.HasMore = hasMore
	for i, chatMessage := range r.ChatMessages {
		if chatMessage == nil || chatMessage.Message == nil || chatMessage.Message.Role != common.MessageRole_User || i-1 < 0 {
			continue
		}
		if task, ok := userMessageID2ChatTasks[chatMessage.ID]; ok {
			r.ChatMessages[i-1].Tasks = chat.ConvertChatTaskDOs2DTOs(task)
		}
	}

	return r, nil
}

func (s *CodingAgentServiceImpl) MGetRollbackChatHistory(ctx context.Context, req *platform.MGetRollbackChatHistoryReq) (r *platform.MGetRollbackChatHistoryResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	r = platform.NewMGetRollbackChatHistoryResp()

	return r, nil
}

func (s *CodingAgentServiceImpl) GetFrontierParams(ctx context.Context, req *platform.GetFrontierParamsReq) (r *platform.GetFrontierParamsResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	r = platform.NewGetFrontierParamsResp()

	frontierConfig := conf.GetFrontierConfig()
	r.DeviceID = utils.GenerateDeviceID(req.ProjectID, int(req.PlatformType))
	r.AccessKey = frontier.GetClient().GetAccessKey(r.DeviceID)
	r.ProductID = int64(frontierConfig.ProductID)
	r.AppID = int64(frontierConfig.AppID)

	return r, nil
}

func (s *CodingAgentServiceImpl) GetRequirement(ctx context.Context, req *platform.GetRequirementReq) (r *platform.GetRequirementResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	r = platform.NewGetRequirementResp()

	requirement, err := file_process.GetRequirementService().GetCodingAgentRequirement(ctx, req.ProjectID)
	if err != nil {
		return r, err
	}
	r.RequirementContent = requirement

	return r, nil
}

func (s *CodingAgentServiceImpl) MGetChatLog(ctx context.Context, req *platform.MGetChatLogReq) (r *platform.MGetChatLogResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	r = platform.NewMGetChatLogResp()

	chatLogList, count, err := chat.GetChatLogService().MGetFullChatLog(ctx, &chat.MGetFullChatLogParams{
		ProjectID:             req.ProjectID,
		AssistantChatID:       req.MessageID,
		ChatLogIDs:            req.ChatLogIDs,
		Offset:                int(req.Offset),
		Limit:                 int(req.Limit),
		OrderAscByCreatedTime: req.GetIsAscend(),
	})
	if err != nil {
		return r, err
	}
	r.ChatLogList = chat.ConvertChatLogDOs2DTOs(chatLogList)
	r.Total = int32(count)

	return r, nil
}

func (s *CodingAgentServiceImpl) GetProjectPlan(ctx context.Context, req *platform.GetProjectPlanReq) (r *platform.GetProjectPlanResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	r = platform.NewGetProjectPlanResp()

	plan, err := file_process.GetPlanService().GetReactV1Plan(ctx, req.ProjectID)
	if err != nil {
		return r, err
	}
	r.ReactPlan = file_process.ConvertReactPlanDO2DTO(plan)

	return r, nil
}

func (s *CodingAgentServiceImpl) GetCodeGenerationProgress(ctx context.Context, req *platform.GetCodeGenerationProgressReq) (r *platform.GetCodeGenerationProgressResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	r = platform.NewGetCodeGenerationProgressResp()

	progress, err := file_process.GetGenerationProgressService().GetCodeGenerationProgress(ctx, req.ProjectID)
	if err != nil {
		return r, err
	}
	r.Progress = file_process.ConvertCodeGenerationProgressDO2DTO(progress)

	return r, nil
}

func (s *CodingAgentServiceImpl) ForkRequirement(ctx context.Context, req *platform.ForkRequirementReq) (r *platform.ForkRequirementResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	r = platform.NewForkRequirementResp()

	requirement, err := file_process.GetRequirementService().GetCodingAgentRequirement(ctx, req.FromProjectID)
	if err != nil {
		return r, err
	}
	err = file_process.GetRequirementService().SaveCodingAgentRequirement(ctx, req.ToProjectID, requirement)
	if err != nil {
		return r, err
	}

	return r, nil
}

func (s *CodingAgentServiceImpl) ForkChatMessage(ctx context.Context, req *platform.ForkChatMessageReq) (r *platform.ForkChatMessageResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	r = platform.NewForkChatMessageResp()

	recentMessagePairs := req.GetRecentMessagePairs()
	if recentMessagePairs <= 0 {
		return r, nil
	}
	err = chat.GetChatCombinationService().ForkChatMessage(ctx, &chat.ForkChatMessageParams{
		FromProjectID:      req.FromProjectID,
		ToProjectID:        req.ToProjectID,
		RecentMessagePairs: recentMessagePairs,
	})
	if err != nil {
		return r, err
	}

	return r, nil
}

func (s *CodingAgentServiceImpl) UpdateRequirement(ctx context.Context, req *platform.UpdateRequirementReq) (r *platform.UpdateRequirementResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	r = platform.NewUpdateRequirementResp()

	err = file_process.GetRequirementService().SaveCodingAgentRequirement(ctx, req.ProjectID, req.Requirement)
	if err != nil {
		return r, err
	}

	return r, nil
}

func (s *CodingAgentServiceImpl) MGetChatTask(ctx context.Context, req *platform.MGetChatTaskReq) (r *platform.MGetChatTaskResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	r = platform.NewMGetChatTaskResp()

	tasks, err := chat.GetChatTaskService().MGetChatTask(ctx, &chat.MGetChatTaskParams{
		ProjectID:             req.ProjectID,
		ChatTaskIDs:           req.ChatTaskIDs,
		OrderAscByCreatedTime: req.IsAscend,
		WithTaskDetail:        true,
	})
	if err != nil {
		return r, err
	}
	if req.GetWithChildren() {
		eg, groupCtx := goroutine_util.NewRecoveredGroup(ctx)
		eg.SetLimit(-1)
		for _, eachTask := range tasks {
			task := eachTask
			eg.Go(func() error {
				task.Children, err = chat.GetChatTaskService().MGetChatTask(groupCtx, &chat.MGetChatTaskParams{
					ProjectID:             req.ProjectID,
					ParentID:              conv.Int64Ptr(task.ID),
					OrderAscByCreatedTime: req.IsAscend,
					WithTaskDetail:        true,
				})
				if err != nil {
					return err
				}
				return nil
			})
		}
		err = eg.Wait()
		if err != nil {
			return nil, err
		}
	}
	r.ChatTaskList = chat.ConvertChatTaskDOs2DTOs(tasks)

	return r, nil
}

// TODO CreateMessage 合并到PackService里，然后revert的时候开事务
func (s *CodingAgentServiceImpl) CreateChatMessage(ctx context.Context, req *platform.CreateChatMessageReq) (r *platform.CreateChatMessageResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	r = platform.NewCreateChatMessageResp()

	err = chat.GetChatHistoryService().CreateChatMessageByType(ctx, &chat.CreateChatMessageByTypeParams{
		UserID:              req.UserID,
		ProjectID:           req.ProjectID,
		Type:                req.Type,
		ConversationMessage: chat.ConvertConversationMessageDTO2DO(req.ConversationMessage),
		RollbackMessage:     chat.ConvertRollbackMessageDTO2DO(req.RollbackMessage),
		VersionMessage:      chat.ConvertVersionMessageDTO2DO(req.VersionMessage),
	})
	if err != nil {
		return r, err
	}

	return r, nil
}

// todo 搞个service出来放一下biz逻辑
func (s *CodingAgentServiceImpl) EventCallback(ctx context.Context, req *platform.EventCallbackReq) (r *platform.EventCallbackResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	r = platform.NewEventCallbackResp()

	if req.Scene != common.CallbackEvent_ReactCompilation || req.ReactCompilationEvent == nil {
		return r, errorx.InvalidParamError
	}
	chatMessage, err := chat.GetChatHistoryService().GetParentChatMessage(ctx, req.ReactCompilationEvent.MessageID, false)
	if err != nil {
		return r, err
	}
	// 再判断一下project的phase？
	if chatMessage.Status != common.ChatMessageStatus_Processing {
		return r, errorx.InvalidParamError
	}
	// todo 事物
	_, err = chat.GetChatHistoryService().UpdateChatMessage(ctx, &chat.UpdateChatMessageParams{
		ProjectID:      req.ReactCompilationEvent.ProjectID,
		ChatMessageIDs: []int64{req.ReactCompilationEvent.MessageID},
		FromStatus:     common.ChatMessageStatusPtr(common.ChatMessageStatus_Processing),
		UpdateStatus:   common.ChatMessageStatusPtr(common.ChatMessageStatus_Completed),
		UpdateMessage: &model.Message{
			Content: strings.Join(req.ReactCompilationEvent.ErrorMessages, "\n"),
		},
	})
	if err != nil {
		return r, err
	}

	return r, nil
}

func (s *CodingAgentServiceImpl) DebugSetRequirement(ctx context.Context, req *platform.DebugSetRequirementReq) (r *platform.DebugSetRequirementResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	r = platform.NewDebugSetRequirementResp()

	err = file_process.GetRequirementService().SaveCodingAgentRequirement(ctx, req.ProjectID, req.Requirement)
	if err != nil {
		return r, err
	}

	return r, nil
}

func (s *CodingAgentServiceImpl) DebugInvokePlanAgent(ctx context.Context, req *platform.DebugInvokePlanAgentReq) (r *platform.DebugInvokePlanAgentResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	r = platform.NewDebugInvokePlanAgentResp()

	chatMessages, err := chat.GetChatHistoryService().MGetChatMessages(ctx, &chat.MGetChatMessagesParams{
		ProjectID:                req.ProjectID,
		ParentID:                 conv.Int64Ptr(0),
		Limit:                    2,
		IsAscend:                 true,
		WithMessageContentDetail: true,
	})
	if err != nil {
		return r, err
	}
	if len(chatMessages) <= 1 || chatMessages[0].Role != common.MessageRole_User || chatMessages[1].Role != common.MessageRole_Assistant {
		return r, errorx.Wrapf(errorx.InvalidParamError, "invalid user chat message")
	}
	projectDO, err := project.GetProjectService().GetByID(ctx, req.ProjectID)
	if err != nil {
		return r, err
	}
	chatAgent := chat.NewChatAgent(&chat.NewChatAgentParams{
		Project:              projectDO,
		UserChatMessage:      chatMessages[0],
		AssistantChatMessage: chatMessages[1],
	})
	_, err = plan_react.NewAgent(chatAgent).GeneratePlan(ctx, &plan_react.PlanGenerateParams{
		Project:           projectDO,
		UserChatMessageID: chatMessages[0].ID,
		Options:           &model.ExecuteAgentOptions{},
	})
	if err != nil {
		return r, err
	}

	return r, nil
}

func (s *CodingAgentServiceImpl) TnsCheckProjectCode(ctx context.Context, req *platform.TnsCheckProjectCodeReq) (res *platform.TnsCheckProjectCodeResp, err error) {
	defer errLogTraffic(ctx, req, &err)
	res = platform.NewTnsCheckProjectCodeResp()
	res.Safe = true
	res.ProjectID = req.ProjectID

	// 获取项目信息
	projectDO, err := project.GetProjectService().GetByID(mysql.DB.UseWriteDB(ctx), req.ProjectID)
	if err != nil {
		return res, errorx.Wrapf(err, "method=ProjectService.GetByID failed")
	}

	// 获取项目代码
	gitService := file_process.GetGitService()
	fs, err := gitService.GetRepositoryFromProject(ctx, &model.Project{
		ID:     req.ProjectID,
		UserID: req.UserID,
	})
	if err != nil {
		logs.CtxError(ctx, "method=GetRepositoryFromProject failed, err=%+v", err)
		return res, err
	}

	// 获取项目模板配置
	projectTemplateConfig := tcc.GetProjectTemplateConfig(ctx, projectDO.ProjectTemplate)
	// FIXME(yb): 对于 project_template_html 项目，ignorePaths 不能写死了，应该从 projectTemplateConfig 中获取
	projectCode, err := file_util.ReadProjectFiles(ctx, fs, "", []string{"src/supabase/migrations"}, projectTemplateConfig)
	if err != nil {
		logs.CtxError(ctx, "method=ReadProjectFiles failed, err=%+v", err)
		return res, err
	}

	tnsClient := tns.GetClient()
	// XXX: 检查项目代码（不用关心检查的结果）
	tnsClient.CheckProjectCode(ctx, &tns.CheckProjectCodeParams{
		ProjectID: req.ProjectID,
		Code:      projectCode,
	})

	return res, nil
}
