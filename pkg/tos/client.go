package tos

import (
	"bytes"
	"context"
	"io"
	"sync"
	"time"

	"github.com/pkg/errors"

	"code.byted.org/gopkg/tos"
	"code.byted.org/kv/backoff"
	"code.byted.org/stone/caribou/utils/log_util"
)

const (
	defaultRetryTime     uint64 = 3
	defaultRetryInterval        = 500 * time.Millisecond

	defaultUploadTimeout   = 3 * 60 * time.Second
	defaultDownloadTimeout = 3 * 60 * time.Second
)

var (
	initOnce  sync.Once
	tosClient *Client
)

type Client struct {
	tosClient     *tos.Tos
	retryTime     uint64
	retryInterval time.Duration
}

func GetClient() *Client {
	return tosClient
}

func newClient(bucketName string, accessKey string) (*Client, error) {
	client, err := tos.NewTos(tos.WithBucket(bucketName),
		tos.WithCredentials(&tos.BucketAccessKeyCredentials{
			BucketName: bucketName,
			AccessKey:  accessKey,
		}))
	if err != nil {
		return nil, errors.Wrapf(err, "method=tos.NewTos failed")
	}
	return &Client{
		tosClient:     client,
		retryTime:     defaultRetryTime,
		retryInterval: defaultRetryInterval,
	}, nil
}

// PutByteObject 设置3分钟上传超时时间
func (c *Client) PutByteObject(ctx context.Context, tosURI string, content []byte) error {
	if len(tosURI) == 0 || len(content) == 0 {
		return errors.New("invalid params")
	}

	ctxChild, cancel := context.WithTimeout(ctx, defaultUploadTimeout)
	defer cancel()
	err := backoff.Retry(func() error {
		return c.tosClient.PutObject(ctxChild, tosURI, int64(len(content)), bytes.NewBuffer(content)) // ignore_security_alert
	}, c.getBackOff())
	if err != nil {
		return errors.Wrapf(err, "method=(*tos.Tos).PutObject failed")
	}
	log_util.CtxInfo(ctx, "method=(*tos.Tos).PutObject success, tosURI=%v", tosURI)

	return nil
}

// PutStringObject 设置3分钟上传超时时间
func (c *Client) PutStringObject(ctx context.Context, tosURI string, content string) error {
	if len(content) == 0 {
		return errors.New("invalid params")
	}

	return c.PutByteObject(ctx, tosURI, []byte(content))
}

// GetObject 设置3分钟下载超时时间
func (c *Client) GetObject(ctx context.Context, tosURI string) ([]byte, error) {
	if len(tosURI) <= 0 {
		return nil, errors.New("invalid params")
	}

	ctxChild, cancel := context.WithTimeout(ctx, defaultDownloadTimeout)
	defer cancel()
	var object *tos.ObjectInfo
	err := backoff.Retry(func() (err error) {
		object, err = c.tosClient.GetObject(ctxChild, tosURI)
		if err != nil {
			return err
		}
		return nil
	}, c.getBackOff())
	if err != nil {
		return nil, errors.Wrapf(err, "method=(*tos.Tos).GetObject failed")
	}
	content, err := io.ReadAll(object.R)
	if err != nil {
		return nil, errors.Wrapf(err, "method=io.ReadAll failed")
	}

	return content, nil
}

func (c *Client) DelObject(ctx context.Context, tosURI string) error {
	if len(tosURI) <= 0 {
		return errors.New("invalid params")
	}

	err := backoff.Retry(func() (err error) {
		err = c.tosClient.DelObject(ctx, tosURI)
		if err != nil {
			return err
		}
		return nil
	}, c.getBackOff())
	if err != nil {
		return errors.Wrapf(err, "method=(*tos.Tos).DelObject failed")
	}

	return nil
}

func (c *Client) getBackOff() backoff.BackOff {
	return backoff.WithMaxRetries(&backoff.ConstantBackOff{Interval: c.retryInterval}, c.retryTime)
}
