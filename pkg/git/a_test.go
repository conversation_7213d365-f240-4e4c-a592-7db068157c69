package git

import (
	"code.byted.org/stone/caribou/utils/file_util"
	"context"
	"fmt"
	"github.com/go-git/go-billy/v5/memfs"
	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing"
	"github.com/go-git/go-git/v5/storage/memory"
	"os"
	"strings"
	"testing"
)

func TestA(t *testing.T) {
	cloneURL := strings.Replace("https://code.byted.org/stone/caribou.git", "://", fmt.Sprintf("://oauth2:%s@", "wZWybwsggTLjHWi2fgsi"), 1)
	// 在内存中克隆仓库
	repo, err := git.Clone(memory.NewStorage(), memfs.New(), &git.CloneOptions{
		URL:      cloneURL,
		Progress: os.Stdout,
	})
	if err != nil {
		panic(err)
	}
	branchName := "feature/travel-agent"
	refName := plumbing.ReferenceName("refs/heads/" + branchName)
	_, err = repo.Reference(refName, true)
	if err != nil {
		refName = plumbing.NewRemoteReferenceName("origin", branchName)
		_, err = repo.Reference(refName, true)
		if err != nil {
			panic(err)
		}
	}

	// 切换到指定分支
	worktree, _ := repo.Worktree()
	err = worktree.Checkout(&git.CheckoutOptions{
		Branch: refName,
		Create: false, // 不创建新分支
	})
	if err != nil {
		panic(err)
	}

	fmt.Printf("Switched to branch '%s'\n", branchName)
}

func TestB(t *testing.T) {
	gitClient, _ := NewGitClient(&NewGitClientParams{
		UseMemory: true,
		Tos: &TosParams{
			TosURL: "https://tosv.byted.org/obj/agent-runtime/datasets_repo/swe101_bench_code_v2/flex-001.zip",
		},
	})
	fs, err := gitClient.GetFS(context.Background())
	if err != nil {
		panic(err)
	}
	content, err := file_util.ReadTargetProjectFiles(context.Background(), fs, []string{"/test/utils/css.js"})
	if err != nil {
		panic(err)
	}
	fmt.Println(string(content))
}
