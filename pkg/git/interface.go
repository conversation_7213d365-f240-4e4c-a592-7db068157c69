package git

import (
	"context"

	"code.byted.org/stone/caribou/utils/file_util"
)

type GitClient interface {
	GetFS(ctx context.Context) (FileSystem, error)
}

type FileSystem interface {
	file_util.FileSystem

	// 提交更改
	Commit(message string, name, email string) (string, error)
	// Push 推送更改到远程仓库
	Push(force bool) error
	// 复制文件
	CopyFile(src, dst string) error
	// 获取仓库状态
	Status() (string, error)
	GetHeadCommitHash() (string, error)
	GetFirstCommitHash() (string, error)
	// 获取提交历史
	LogCommits() (string, error)
	Reset(commitHash string) error

	CheckoutBranch(branchName string) (err error)

	CheckoutNewBranch(baseBranch, newBranchName string) (exist bool, err error)
}

// Repository 仓库信息
type Repository struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
	// ... 其他字段可以根据需要添加
}

type GetFileContentParams struct {
	CommitHash string
	FilePath   string
}
