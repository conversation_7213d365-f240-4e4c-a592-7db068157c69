package git

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing"
	"github.com/go-git/go-git/v5/plumbing/object"
	"github.com/pkg/errors"

	"code.byted.org/stone/caribou/utils/file_util"
)

// fileSystem 实现 FileSystem 接口
type fileSystem struct {
	file_util.FileSystem
	repo     *git.Repository
	worktree *git.Worktree

	codebase *CodebaseParams
	tos      *TosParams
}

// Commit 提交更改
func (fs *fileSystem) Commit(message string, name, email string) (commitHash string, err error) {
	if fs.tos != nil {
		return "", nil
	}

	// 添加所有更改
	hash, err := fs.worktree.Add(".")
	if err != nil {
		return "", errors.Wrapf(err, "method=(*WorkTree).Add failed")
	}

	// 检查是否有更改需要提交
	status, err := fs.worktree.Status()
	if err != nil {
		return "", errors.Wrapf(err, "method=(*WorkTree).Status failed")
	}

	if !status.IsClean() {
		hash, err = fs.worktree.Commit(message, &git.CommitOptions{
			Author: &object.Signature{
				Name:  name,
				Email: email,
				When:  time.Now(),
			},
		})
		if err != nil {
			return "", errors.Wrapf(err, "method=(*WorkTree).Commit failed")
		}
	}

	return hash.String(), nil
}

// Push 推送更改到远程仓库
func (fs *fileSystem) Push(force bool) error {
	if fs.tos != nil {
		return nil
	}

	// 更新远程URL
	_, err := fs.repo.Remote("origin")
	if err != nil {
		return errors.Wrapf(err, "method=(*Repository).Remote failed")
	}

	err = fs.repo.Push(&git.PushOptions{
		RemoteName: "origin",
		RemoteURL:  strings.Replace(fs.codebase.URL, "://", fmt.Sprintf("://oauth2:%s@", fs.codebase.Token), 1),
		Auth:       nil,
		Force:      force,
		Progress:   os.Stdout,
	})
	if err != nil {
		if errors.Is(err, git.NoErrAlreadyUpToDate) {
			fmt.Println("Remote repository is already up to date")
			return nil
		}
		return errors.Wrapf(err, "method=(*Repository).Push failed")
	}

	fmt.Println("Successfully pushed to remote repository")
	return nil
}

// Status 获取仓库状态
func (fs *fileSystem) Status() (string, error) {
	if fs.tos != nil {
		return "", nil
	}

	status, err := fs.worktree.Status()
	if err != nil {
		return "", errors.Wrapf(err, "method=(*WorkTree).Status failed")
	}

	return status.String(), nil
}

func (fs *fileSystem) GetHeadCommitHash() (string, error) {
	if fs.tos != nil {
		return "", nil
	}

	ref, err := fs.repo.Head()
	if err != nil {
		return "", errors.Wrapf(err, "method=(*Repository).Head failed")
	}
	// 获取最近一次commit对象
	commit, err := fs.repo.CommitObject(ref.Hash())
	if err != nil {
		return "", errors.Wrapf(err, "method=(*Repository).CommitObject failed")
	}

	return commit.Hash.String(), nil
}

func (fs *fileSystem) GetFirstCommitHash() (string, error) {
	if fs.tos != nil {
		return "", nil
	}

	iter, err := fs.repo.Log(&git.LogOptions{})
	if err != nil {
		return "", errors.Wrapf(err, "method=(*Repository).Log failed")
	}
	var firstCommit *object.Commit
	err = iter.ForEach(func(c *object.Commit) error {
		// 如果没有父提交，则说明找到了初始提交
		if len(c.ParentHashes) == 0 {
			firstCommit = c
			return nil // 停止遍历
		}
		return nil
	})
	if err != nil {
		return "", errors.Wrapf(err, "method=CommitIter.ForEach failed")
	}

	return firstCommit.Hash.String(), nil
}

// LogCommits 获取提交历史
func (fs *fileSystem) LogCommits() (string, error) {
	if fs.tos != nil {
		return "", nil
	}

	var logOutput strings.Builder
	// 获取提交历史
	commits, err := fs.repo.Log(&git.LogOptions{})
	if err != nil {
		return "", errors.Wrapf(err, "method=(*Repository).Log failed")
	}
	defer commits.Close()

	// 遍历提交
	err = commits.ForEach(func(c *object.Commit) error {
		logOutput.WriteString(fmt.Sprintf("Commit: %s\n", c.Hash))
		logOutput.WriteString(fmt.Sprintf("Author: %s <%s>\n", c.Author.Name, c.Author.Email))
		logOutput.WriteString(fmt.Sprintf("Date: %s\n", c.Author.When))
		logOutput.WriteString(fmt.Sprintf("\n    %s\n\n", c.Message))
		return nil
	})
	if err != nil {
		return "", fmt.Errorf("iterate commits failed: %w", err)
	}

	return logOutput.String(), nil
}

func (fs *fileSystem) Reset(commitHash string) error {
	if fs.tos != nil {
		return nil
	}

	hash := plumbing.NewHash(commitHash)
	err := fs.worktree.Reset(&git.ResetOptions{
		Commit: hash,
		Mode:   git.HardReset,
	})
	if err != nil {
		return errors.Wrapf(err, "method=(*Repository).Checkout failed")
	}

	return nil
}

func (fs *fileSystem) CheckoutNewBranch(baseBranch, newBranchName string) (exist bool, err error) {
	if fs.tos != nil {
		return false, nil
	}

	// 确定基础分支
	baseRefName := plumbing.ReferenceName(filepath.Join("refs/heads/", baseBranch))
	var baseRef *plumbing.Reference
	if len(baseBranch) > 0 {
		// 基于指定分支创建
		baseRef, err = fs.repo.Reference(baseRefName, true)
		if err != nil {
			baseRefName = plumbing.NewRemoteReferenceName("origin", baseBranch)
			baseRef, err = fs.repo.Reference(baseRefName, true)
			if err != nil {
				return false, errors.Wrapf(err, "method=(*Repository).Reference failed")
			}
		}
	} else {
		// 基于当前 HEAD 创建
		baseRef, err = fs.repo.Head()
		if err != nil {
			return false, errors.Wrapf(err, "method=(*Repository).Head failed")
		}
	}
	// 检查分支是否已存在
	_, err = fs.repo.Reference(plumbing.ReferenceName("refs/heads/"+newBranchName), true)
	if err == nil {
		return true, nil
	}
	_, err = fs.repo.Reference(plumbing.NewRemoteReferenceName("origin", newBranchName), true)
	if err == nil {
		return true, nil
	}
	// 创建新分支引用
	newBranchRef := plumbing.NewHashReference(
		plumbing.ReferenceName("refs/heads/"+newBranchName),
		baseRef.Hash(),
	)
	err = fs.repo.Storer.SetReference(newBranchRef)
	if err != nil {
		return false, errors.Wrapf(err, "method=ReferenceStorer.SetReference failed")
	}
	// 切换到新分支
	err = fs.worktree.Checkout(&git.CheckoutOptions{
		Branch: plumbing.ReferenceName("refs/heads/" + newBranchName),
		Create: false, // 分支已经创建了
	})
	if err != nil {
		return false, errors.Wrapf(err, "method=(*WorkTree).Checkout failed")
	}
	fmt.Printf("Switched to a new branch '%s'\n", newBranchName)

	return false, nil
}

func (fs *fileSystem) CheckoutBranch(branchName string) (err error) {
	if fs.tos != nil {
		return nil
	}

	// 检查分支是否存在
	refName := plumbing.ReferenceName("refs/heads/" + branchName)
	_, err = fs.repo.Reference(refName, true)
	if err != nil {
		refName = plumbing.NewRemoteReferenceName("origin", branchName)
		_, err = fs.repo.Reference(refName, true)
		if err != nil {
			return errors.Wrapf(err, "branch '%s' does not exist", branchName)
		}
	}

	// 切换到指定分支
	err = fs.worktree.Checkout(&git.CheckoutOptions{
		Branch: refName,
		Create: false, // 不创建新分支
	})
	if err != nil {
		return errors.Wrapf(err, "method=(*WorkTree).Checkout failed")
	}

	fmt.Printf("Switched to branch '%s'\n", branchName)

	return nil
}
