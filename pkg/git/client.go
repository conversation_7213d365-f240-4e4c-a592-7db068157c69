package git

import (
	"archive/zip"
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/go-git/go-billy/v5"
	"github.com/go-git/go-billy/v5/memfs"
	"github.com/go-git/go-billy/v5/osfs"
	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/storage/memory"
	"github.com/google/uuid"
	"github.com/pkg/errors"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/stone/caribou/utils/file_util"
	"code.byted.org/stone/caribou/utils/log_util"
)

func NewGitClient(params *NewGitClientParams) (GitClient, error) {
	if params.Codebase != nil && params.Tos != nil {
		return nil, errors.New("cannot use Codebase and Tos at the same time")
	}
	if params.Codebase == nil && params.Tos == nil {
		return nil, errors.New("must use Codebase or Tos")
	}
	if !params.UseMemory && params.LocalPath == nil {
		return nil, errors.New("must specify LocalPath when UseMemory is true")
	}

	repoName := params.RepoName
	if len(repoName) <= 0 {
		repoName = uuid.New().String()
	}

	return &client{
		useMemory: params.UseMemory,
		localPath: filepath.Join(conv.StringDefault(params.LocalPath, os.TempDir()), repoName),
		codebase:  params.Codebase,
		tos:       params.Tos,
	}, nil
}

type NewGitClientParams struct {
	UseMemory bool
	LocalPath *string
	RepoName  string
	Codebase  *CodebaseParams
	Tos       *TosParams
}

type CodebaseParams struct {
	URL   string
	Token string
}

type TosParams struct {
	TosURL string
}

type client struct {
	useMemory bool
	localPath string
	repoName  string
	codebase  *CodebaseParams
	tos       *TosParams
}

func (c *client) GetFS(ctx context.Context) (fs FileSystem, err error) {
	startTime := time.Now()
	defer func() {
		log_util.CtxInfo(ctx, "method=GetFS invoked, cost_msec=%v", time.Since(startTime).Milliseconds())
	}()

	var (
		repo       *git.Repository
		worktree   *git.Worktree
		filesystem billy.Filesystem
		targetDir  string
	)
	if c.codebase != nil {
		if c.useMemory {
			repo, err = c.cloneRepoInMemory(ctx)
		} else {
			// 使用临时目录或指定目录
			repo, err = c.cloneRepo(ctx, c.localPath)
		}
		if err != nil {
			return nil, errors.Wrapf(err, "method=(*client).cloneRepo failed")
		}
		worktree, err = repo.Worktree()
		if err != nil {
			return nil, errors.Wrapf(err, "method=(*git.Repository).Worktree failed")
		}
		filesystem = worktree.Filesystem
	} else {
		filesystem, err = c.cloneTosRepo(ctx)
		if err != nil {
			return nil, errors.Wrapf(err, "method=(*client).cloneTosRepo failed")
		}
	}

	return &fileSystem{
		FileSystem: file_util.NewFileSystem(filesystem, c.useMemory, targetDir),
		repo:       repo,
		worktree:   worktree,
		codebase:   c.codebase,
		tos:        c.tos,
	}, nil
}

func (c *client) cloneRepo(ctx context.Context, targetDir string) (*git.Repository, error) {
	// 如果目标目录已存在且包含.git目录，直接打开现有仓库
	if _, err := os.Stat(targetDir); err == nil {
		if _, err := os.Stat(filepath.Join(targetDir, ".git")); err == nil {
			repo, err := git.PlainOpen(targetDir)
			if err == nil {
				return repo, nil // 已存在且可以打开，直接返回
			}
		}
	}
	cloneURL := strings.Replace(c.codebase.URL, "://", fmt.Sprintf("://oauth2:%s@", c.codebase.Token), 1)
	// 确保父目录存在
	err := os.MkdirAll(filepath.Dir(targetDir), 0755)
	if err != nil {
		return nil, errors.Wrapf(err, "method=os.MkdirAll failed")
	}
	// 克隆仓库
	repo, err := git.PlainClone(targetDir, false, &git.CloneOptions{
		URL:      cloneURL,
		Progress: os.Stdout,
	})
	if err != nil {
		return nil, errors.Wrapf(err, "method=git.PlainClone failed")
	}
	log_util.CtxInfo(ctx, "Repository cloned successfully! dir=%s", targetDir)

	return repo, nil
}

func (c *client) cloneRepoInMemory(ctx context.Context) (*git.Repository, error) {
	// 构建克隆URL
	cloneURL := strings.Replace(c.codebase.URL, "://", fmt.Sprintf("://oauth2:%s@", c.codebase.Token), 1)
	// 在内存中克隆仓库
	repo, err := git.Clone(memory.NewStorage(), memfs.New(), &git.CloneOptions{
		URL:      cloneURL,
		Progress: os.Stdout,
	})
	if err != nil {
		return nil, errors.Wrapf(err, "clone repository to memory failed")
	}
	log_util.CtxInfo(ctx, "Repository cloned to memory successfully!")

	return repo, nil
}

func (c *client) cloneTosRepo(ctx context.Context) (billy.Filesystem, error) {
	if c.tos == nil || len(c.tos.TosURL) <= 0 {
		return nil, errors.New("invalid params")
	}

	// 1. 下载ZIP文件
	resp, err := http.Get(c.tos.TosURL)
	if err != nil {
		return nil, errors.Wrapf(err, "method=http.Get failed")
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return nil, errors.New(fmt.Sprintf("bad status: %s", resp.Status))
	}
	// 2. 读取响应体到内存
	zipData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.Wrapf(err, "method=io.ReadAll failed")
	}
	// 3. 解析ZIP到billy.Filesystem
	fs, err := c.convertZip2Filesystem(zipData)
	if err != nil {
		return nil, errors.Wrapf(err, "method=convertZip2Files failed")
	}
	if c.useMemory {
		log_util.CtxInfo(ctx, "Repository cloned to memory successfully!")
	} else {
		log_util.CtxInfo(ctx, "Repository cloned successfully! dir=%s", c.localPath)
	}

	return fs, nil
}

// ZipBytesToFilesystem 将ZIP字节数据解析到billy.Filesystem
func (c *client) convertZip2Filesystem(zipData []byte) (fs billy.Filesystem, err error) {
	if c.useMemory {
		fs = memfs.New()
	} else {
		if err := os.MkdirAll(c.localPath, 0755); err != nil {
			return nil, errors.Wrapf(err, "method=os.MkdirAll failed")
		}
		fs = osfs.New(c.localPath)
	}
	// 创建ZIP阅读器
	zipReader, err := zip.NewReader(bytes.NewReader(zipData), int64(len(zipData)))
	if err != nil {
		return nil, fmt.Errorf("failed to create zip reader: %w", err)
	}
	// 遍历ZIP中的所有文件
	for _, file := range zipReader.File {
		err := extractFile2Filesystem(fs, file)
		if err != nil {
			return nil, fmt.Errorf("failed to extract file %s: %w", file.Name, err)
		}
	}

	return fs, nil
}

// extractFileToFilesystem 提取单个文件到文件系统
func extractFile2Filesystem(fs billy.Filesystem, file *zip.File) error {
	// 标准化路径（将反斜杠替换为正斜杠）
	path := filepath.ToSlash(file.Name)
	// 跳过空路径
	if len(path) <= 0 {
		return nil
	}
	// 如果是目录
	if file.FileInfo().IsDir() || strings.HasSuffix(path, "/") {
		// 确保目录路径不以斜杠结尾（billy的要求）
		if strings.HasSuffix(path, "/") {
			path = path[:len(path)-1]
		}
		if path != "" {
			return fs.MkdirAll(path, file.FileInfo().Mode())
		}
		return nil
	}
	// 创建父目录
	if dir := filepath.Dir(path); dir != "." {
		err := fs.MkdirAll(dir, 0755)
		if err != nil {
			return fmt.Errorf("failed to create directory %s: %w", dir, err)
		}
	}
	// 打开ZIP中的文件
	rc, err := file.Open()
	if err != nil {
		return fmt.Errorf("failed to open file in zip: %w", err)
	}
	defer rc.Close()
	// 在文件系统中创建文件
	outFile, err := fs.Create(path)
	if err != nil {
		return fmt.Errorf("failed to create file in filesystem: %w", err)
	}
	defer outFile.Close()
	// 复制文件内容
	_, err = io.Copy(outFile, rc)
	if err != nil {
		return fmt.Errorf("failed to copy file content: %w", err)
	}

	return nil
}
