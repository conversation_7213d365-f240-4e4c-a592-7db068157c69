package fornax

import (
	"context"
	"strings"

	"github.com/cloudwego/eino/callbacks"
	"github.com/cloudwego/eino/components/prompt"
	"github.com/cloudwego/eino/schema"
	"github.com/pkg/errors"

	eino_fornax "code.byted.org/flow/eino-byted-ext/callbacks/fornax"
	"code.byted.org/flowdevops/fornax_sdk"
	fornax_prompt "code.byted.org/flowdevops/fornax_sdk/domain/prompt"
	"code.byted.org/flowdevops/fornax_sdk/infra/ob"
	"code.byted.org/stone/caribou/utils"
)

func GetClient() FornaxClient {
	return fornaxClientImpl
}

var fornaxClientImpl *fornaxClient

type fornaxClient struct {
	client *fornax_sdk.Client
}

func (f *fornaxClient) GetPromptTemplate(ctx context.Context, params *GetPromptParams) (prompt.ChatTemplate, error) {
	if err := utils.Validate(params); err != nil {
		return nil, err
	}

	var version *string
	if params.Version != nil && len(*params.Version) > 0 {
		version = params.Version
	}
	fornaxPrompt, err := f.client.GetPrompt(ctx, &fornax_prompt.GetPromptParam{
		Key:     params.Key,
		Version: version,
	})
	if err != nil {
		return nil, errors.Wrapf(err, "method=(*fornax_sdk.Client).GetPrompt failed")
	}
	if fornaxPrompt == nil || fornaxPrompt.Prompt == nil || fornaxPrompt.Prompt.PromptText == nil {
		return nil, errors.New("fornax prompt is nil")
	}
	templates := make([]schema.MessagesTemplate, 0, len(fornaxPrompt.Prompt.PromptText.MessageList))
	if fornaxPrompt.Prompt.PromptText.SystemPrompt != nil &&
		(len(fornaxPrompt.Prompt.PromptText.SystemPrompt.Content) > 0 || len(fornaxPrompt.Prompt.PromptText.SystemPrompt.Parts) > 0) {
		templates = append(templates, schema.SystemMessage(fornaxPrompt.Prompt.PromptText.SystemPrompt.Content))
	}
	for _, msg := range fornaxPrompt.Prompt.PromptText.MessageList {
		switch msg.MessageType {
		case fornax_prompt.MessageTypeSystem:
			templates = append(templates, schema.SystemMessage(msg.Content))
		case fornax_prompt.MessageTypeUser:
			templates = append(templates, schema.UserMessage(msg.Content))
		case fornax_prompt.MessageTypeAssistant:
			templates = append(templates, schema.AssistantMessage(msg.Content, nil))
		case fornax_prompt.MessageTypePlaceholder:
			templates = append(templates, schema.MessagesPlaceholder(getPlaceholderKey(msg.Content), true))
		}
	}

	return &DefaultChatTemplate{einoDefaultChatTemplate: prompt.FromMessages(schema.Jinja2, templates...)}, nil
}

func (f *fornaxClient) GetMessages(ctx context.Context, params *GetMessagesParams) ([]*schema.Message, error) {
	promptTemplate, err := f.GetPromptTemplate(ctx, &GetPromptParams{Key: params.Key, Version: params.Version})
	if err != nil {
		return nil, errors.Wrapf(err, "method=(*fornaxClient).GetPromptTemplate failed")
	}

	return promptTemplate.Format(ctx, params.Variables)
}

func (f *fornaxClient) NewDefaultCallbackHandler() callbacks.Handler {
	return eino_fornax.NewDefaultCallbackHandler(f.client)
}

func (f *fornaxClient) StartSpan(ctx context.Context, name, spanType string, opts ...ob.FornaxStartSpanOption) (ob.FornaxSpan, context.Context, error) {
	return f.client.StartSpan(ctx, name, spanType, opts...)
}

func (f *fornaxClient) StartModelSpan(ctx context.Context, name string, opts ...ob.FornaxStartSpanOption) (ob.ModelSpan, context.Context, error) {
	return f.client.StartModelSpan(ctx, name, opts...)
}

func (f *fornaxClient) Close(ctx context.Context) error {
	fornax_sdk.Close()

	return nil
}

func getPlaceholderKey(input string) string {
	// 检查输入是否以 {{ 开头并以 }} 结尾
	if strings.HasPrefix(input, "{{") && strings.HasSuffix(input, "}}") {
		// 去掉 {{ 和 }}
		return input[2 : len(input)-2]
	}
	return input
}
