package fornax

import (
	"context"

	"github.com/cloudwego/eino/callbacks"
	"github.com/cloudwego/eino/components/prompt"
	"github.com/cloudwego/eino/schema"

	"code.byted.org/flowdevops/fornax_sdk/infra/ob"
)

type FornaxClient interface {
	GetPromptTemplate(ctx context.Context, params *GetPromptParams) (prompt.ChatTemplate, error)

	GetMessages(ctx context.Context, params *GetMessagesParams) ([]*schema.Message, error)

	NewDefaultCallbackHandler() callbacks.Handler

	StartSpan(ctx context.Context, name, spanType string, opts ...ob.FornaxStartSpanOption) (ob.FornaxSpan, context.Context, error)

	StartModelSpan(ctx context.Context, name string, opts ...ob.FornaxStartSpanOption) (ob.ModelSpan, context.Context, error)

	Close(ctx context.Context) error
}

type GetPromptParams struct {
	Key     string `validate:"required,gt=0"`
	Version *string
}

type GetMessagesParams struct {
	Key       string `validate:"required,gt=0"`
	Version   *string
	Variables map[string]any
}

var _ prompt.ChatTemplate = &DefaultChatTemplate{}

type DefaultChatTemplate struct {
	einoDefaultChatTemplate *prompt.DefaultChatTemplate
}

func (t *DefaultChatTemplate) Format(ctx context.Context, vs map[string]any, opts ...prompt.Option) (result []*schema.Message, err error) {
	originMessages, err := t.einoDefaultChatTemplate.Format(ctx, vs, opts...)
	if err != nil {
		return nil, err
	}

	for _, eachMessage := range originMessages {
		if eachMessage == nil {
			continue
		}
		result = append(result, eachMessage)
	}

	return result, nil
}
